import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { NavigationDashboardService } from '../../services/navigation-dashboard.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {
  private authService = inject(AuthService);
  private dashboardService = inject(NavigationDashboardService);

  currentUser$ = this.authService.getCurrentUser();
  currentDashboard$ = this.dashboardService.getCurrentDashboard();
  currentDashboard: 'client' | 'worker' = 'client';

  ngOnInit(): void {
    this.currentDashboard$.subscribe(dashboard => {
      this.currentDashboard = dashboard;
    });
  }

  switchDashboard(): void {
    if (this.currentDashboard === 'client') {
      this.dashboardService.switchToWorkerDashboard();
    } else {
      this.dashboardService.switchToClientDashboard();
    }
  }

  isWorker(): boolean {
    return this.authService.isWorker();
  }

    logout(): void {
    this.authService.logout();
  }
}
