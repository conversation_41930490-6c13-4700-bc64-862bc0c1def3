import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { WorkerControllerService, WorkerResponseDto } from '../../generated-sources/openapi';

@Component({
  selector: 'app-worker-detail',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './worker-detail.component.html',
  styleUrl: './worker-detail.component.css'
})
export class WorkerDetailComponent implements OnInit {
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private workerService = inject(WorkerControllerService);

  worker: WorkerResponseDto | null = null;
  isLoading = false;
  error: string | null = null;
  workerId: string | null = null;

  ngOnInit(): void {
    this.workerId = this.route.snapshot.paramMap.get('id');
    if (this.workerId) {
      this.loadWorkerDetail();
    } else {
      this.error = 'Worker ID not found';
    }
  }

  loadWorkerDetail(): void {
    if (!this.workerId) return;

    this.isLoading = true;
    this.error = null;

    this.workerService.getWorkerById(this.workerId).subscribe({
      next: (worker) => {
        this.worker = worker;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading worker details:', error);
        this.error = 'Failed to load worker details. Please try again.';
        this.isLoading = false;
      }
    });
  }

  editWorker(): void {
    if (this.workerId) {
      this.router.navigate(['/workers', this.workerId, 'edit']);
    }
  }

  goBack(): void {
    this.router.navigate(['/workers']);
  }

  getStarRating(rating: number | undefined): string[] {
    const stars = [];
    const ratingValue = rating || 0;

    for (let i = 1; i <= 5; i++) {
      if (i <= ratingValue) {
        stars.push('★');
      } else {
        stars.push('☆');
      }
    }
    return stars;
  }

  callWorker(): void {
    if (this.worker?.phoneNumber) {
      window.open(`tel:${this.worker.phoneNumber}`, '_self');
    }
  }

  getDirections(): void {
    if (this.worker?.location) {
      const encodedLocation = encodeURIComponent(this.worker.location);
      window.open(`https://www.google.com/maps/search/${encodedLocation}`, '_blank');
    }
  }
}
