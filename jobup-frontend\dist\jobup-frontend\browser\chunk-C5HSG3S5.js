import{a as F,c as z,d as D,e as N,f as j,g as I,h as A,i as V,j as B,l as G,m as J}from"./chunk-XXRJEYVX.js";import{d as L}from"./chunk-H277OY4J.js";import{Ca as h,Ea as c,Ga as r,Ha as n,Ia as w,Ja as b,Ka as m,La as p,Ma as o,Na as g,Oa as f,Pa as v,R as k,Ra as O,Sa as y,U as x,Ua as P,aa as u,ba as _,ib as M,jb as S,lb as W,mb as E,ra as s,ub as T}from"./chunk-T3NICLM4.js";function R(a,d){if(a&1&&(r(0,"option",19),o(1),n()),a&2){let e=d.$implicit;c("value",e),s(),f(" ",e," ")}}function $(a,d){a&1&&(r(0,"div",20),w(1,"div",21),r(2,"p"),o(3,"Loading workers..."),n()())}function Y(a,d){if(a&1){let e=b();r(0,"div",22)(1,"i",23),o(2,"\u26A0"),n(),o(3),r(4,"button",24),m("click",function(){u(e);let i=p();return _(i.loadAllWorkers())}),o(5,"Retry"),n()()}if(a&2){let e=p();s(3),f(" ",e.error," ")}}function H(a,d){if(a&1&&(r(0,"span",43),o(1),n()),a&2){let e=d.$implicit;s(),g(e)}}function U(a,d){if(a&1){let e=b();r(0,"div",27),m("click",function(){let i=u(e).$implicit,l=p(2);return _(l.viewWorkerDetail(i.id))}),r(1,"div",28)(2,"div",29),o(3),n(),r(4,"div",30)(5,"h3",31),o(6),n(),r(7,"p",32),o(8),n()()(),r(9,"div",33)(10,"div",34)(11,"i",35),o(12,"\u{1F4CD}"),n(),r(13,"span"),o(14),n()(),r(15,"div",34)(16,"i",35),o(17,"\u{1F4DE}"),n(),r(18,"span"),o(19),n()(),r(20,"div",36)(21,"span",37),h(22,H,2,1,"span",38),n(),r(23,"span",39),o(24),n()()(),r(25,"div",40)(26,"p"),o(27),y(28,"slice"),n()(),r(29,"div",41)(30,"button",42),m("click",function(i){let l=u(e).$implicit;return p(2).viewWorkerDetail(l.id),_(i.stopPropagation())}),o(31," View Details "),n()()()}if(a&2){let e,t=d.$implicit,i=p(2);s(3),f(" ",t.fullName==null||(e=t.fullName.charAt(0))==null?null:e.toUpperCase()," "),s(3),g(t.fullName),s(2),g(t.jobType),s(6),g(t.location),s(5),g(t.phoneNumber),s(3),c("ngForOf",i.getStarRating(t.rating)),s(2),f("(",t.rating||0,"/5)"),s(3),v("",P(28,9,t.description,0,100),"",((t.description==null?null:t.description.length)||0)>100?"...":"","")}}function q(a,d){if(a&1&&(r(0,"div",25),h(1,U,32,13,"div",26),n()),a&2){let e=p();s(),c("ngForOf",e.filteredWorkers)}}function K(a,d){if(a&1){let e=b();r(0,"div",44)(1,"div",45),o(2,"\u{1F50D}"),n(),r(3,"h3"),o(4,"No workers found"),n(),r(5,"p"),o(6,"Try adjusting your search criteria or create a new worker profile."),n(),r(7,"button",2),m("click",function(){u(e);let i=p();return _(i.createNewWorker())}),o(8," Create New Profile "),n()()}}var ae=(()=>{class a{constructor(){this.fb=k(G),this.workerService=k(T),this.router=k(L),this.workers=[],this.filteredWorkers=[],this.isLoading=!1,this.error=null,this.jobTypes=["All","Plumber","Electrician","Carpenter","Painter","Cleaner","Gardener","Mechanic","Cook","Driver","Handyman","Other"],this.searchForm=this.fb.group({jobType:["All"],location:[""]}),this.searchForm.valueChanges.subscribe(()=>{this.filterWorkers()})}ngOnInit(){this.loadAllWorkers()}loadAllWorkers(){this.isLoading=!0,this.error=null,this.workerService.getAllWorkers().subscribe({next:e=>{this.workers=e,this.filteredWorkers=e,this.isLoading=!1},error:e=>{console.error("Error loading workers:",e),this.error="Failed to load workers. Please try again.",this.isLoading=!1}})}filterWorkers(){let{jobType:e,location:t}=this.searchForm.value;this.filteredWorkers=this.workers.filter(i=>{let l=e==="All"||i.jobType===e,C=!t||i.location&&i.location.toLowerCase().includes(t.toLowerCase());return l&&C})}searchByJobType(){let e=this.searchForm.get("jobType")?.value;e&&e!=="All"?(this.isLoading=!0,this.workerService.searchByJobType(e).subscribe({next:t=>{this.workers=t,this.filterWorkers(),this.isLoading=!1},error:t=>{console.error("Error searching by job type:",t),this.error="Failed to search workers. Please try again.",this.isLoading=!1}})):this.loadAllWorkers()}searchByLocation(){let e=this.searchForm.get("location")?.value;e?(this.isLoading=!0,this.workerService.searchByLocation(e).subscribe({next:t=>{this.workers=t,this.filterWorkers(),this.isLoading=!1},error:t=>{console.error("Error searching by location:",t),this.error="Failed to search workers. Please try again.",this.isLoading=!1}})):this.loadAllWorkers()}viewWorkerDetail(e){e&&this.router.navigate(["/workers",e])}createNewWorker(){this.router.navigate(["/workers/create"])}clearSearch(){this.searchForm.reset({jobType:"All",location:""}),this.loadAllWorkers()}getStarRating(e){let t=[],i=e||0;for(let l=1;l<=5;l++)l<=i?t.push("\u2605"):t.push("\u2606");return t}static{this.\u0275fac=function(t){return new(t||a)}}static{this.\u0275cmp=x({type:a,selectors:[["app-worker-list"]],standalone:!0,features:[O],decls:32,vars:7,consts:[[1,"worker-list-container"],[1,"header"],[1,"create-btn",3,"click"],[1,"plus-icon"],[1,"search-section"],[1,"search-form",3,"formGroup"],[1,"search-row"],[1,"search-group"],["for","jobType",1,"search-label"],["id","jobType","formControlName","jobType",1,"search-select",3,"change"],[3,"value",4,"ngFor","ngForOf"],["for","location",1,"search-label"],["type","text","id","location","formControlName","location","placeholder","Enter city or area",1,"search-input",3,"input"],["type","button",1,"clear-btn",3,"click"],[1,"results-summary"],["class","loading-container",4,"ngIf"],["class","error-message",4,"ngIf"],["class","workers-grid",4,"ngIf"],["class","empty-state",4,"ngIf"],[3,"value"],[1,"loading-container"],[1,"loading-spinner"],[1,"error-message"],[1,"error-icon"],[1,"retry-btn",3,"click"],[1,"workers-grid"],["class","worker-card",3,"click",4,"ngFor","ngForOf"],[1,"worker-card",3,"click"],[1,"worker-header"],[1,"worker-avatar"],[1,"worker-info"],[1,"worker-name"],[1,"worker-job"],[1,"worker-details"],[1,"detail-item"],[1,"icon"],[1,"detail-item","rating"],[1,"stars"],["class","star",4,"ngFor","ngForOf"],[1,"rating-text"],[1,"worker-description"],[1,"worker-actions"],[1,"view-btn",3,"click"],[1,"star"],[1,"empty-state"],[1,"empty-icon"]],template:function(t,i){t&1&&(r(0,"div",0)(1,"div",1)(2,"h1"),o(3,"Find Workers"),n(),r(4,"p"),o(5,"Browse and search for skilled workers in your area"),n(),r(6,"button",2),m("click",function(){return i.createNewWorker()}),r(7,"i",3),o(8,"+"),n(),o(9," Create New Profile "),n()(),r(10,"div",4)(11,"form",5)(12,"div",6)(13,"div",7)(14,"label",8),o(15,"Job Type"),n(),r(16,"select",9),m("change",function(){return i.searchByJobType()}),h(17,R,2,2,"option",10),n()(),r(18,"div",7)(19,"label",11),o(20,"Location"),n(),r(21,"input",12),m("input",function(){return i.searchByLocation()}),n()(),r(22,"div",7)(23,"button",13),m("click",function(){return i.clearSearch()}),o(24," Clear Filters "),n()()()()(),r(25,"div",14)(26,"p"),o(27),n()(),h(28,$,4,0,"div",15)(29,Y,6,1,"div",16)(30,q,2,1,"div",17)(31,K,9,0,"div",18),n()),t&2&&(s(11),c("formGroup",i.searchForm),s(6),c("ngForOf",i.jobTypes),s(10),f("",i.filteredWorkers.length," worker(s) found"),s(),c("ngIf",i.isLoading),s(),c("ngIf",i.error),s(),c("ngIf",!i.isLoading&&!i.error),s(),c("ngIf",!i.isLoading&&!i.error&&i.filteredWorkers.length===0))},dependencies:[E,M,S,W,J,N,V,B,F,A,z,D,j,I],styles:[".worker-list-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#f5f7fa,#c3cfe2);padding:2rem}.header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem;position:relative}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2d3748;font-size:2.5rem;font-weight:700;margin-bottom:.5rem}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;font-size:1.1rem;margin-bottom:1.5rem}.create-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:.75rem 1.5rem;border-radius:8px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .2s ease;display:inline-flex;align-items:center;gap:.5rem}.create-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.plus-icon[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.search-section[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:1.5rem;margin-bottom:2rem;box-shadow:0 4px 6px #0000000d}.search-form[_ngcontent-%COMP%]{width:100%}.search-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr auto;gap:1rem;align-items:end}.search-group[_ngcontent-%COMP%]{display:flex;flex-direction:column}.search-label[_ngcontent-%COMP%]{font-weight:600;color:#2d3748;margin-bottom:.5rem;font-size:.9rem}.search-select[_ngcontent-%COMP%], .search-input[_ngcontent-%COMP%]{padding:.75rem;border:2px solid #e2e8f0;border-radius:8px;font-size:1rem;transition:all .2s ease}.search-select[_ngcontent-%COMP%]:focus, .search-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.clear-btn[_ngcontent-%COMP%]{background:#f7fafc;color:#4a5568;border:2px solid #e2e8f0;padding:.75rem 1rem;border-radius:8px;font-size:.9rem;cursor:pointer;transition:all .2s ease}.clear-btn[_ngcontent-%COMP%]:hover{background:#edf2f7;border-color:#cbd5e0}.results-summary[_ngcontent-%COMP%]{margin-bottom:1.5rem}.results-summary[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4a5568;font-weight:500}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:3rem}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #e2e8f0;border-top:4px solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.error-message[_ngcontent-%COMP%]{background:#fed7d7;border:1px solid #feb2b2;color:#c53030;padding:1rem;border-radius:8px;margin-bottom:1.5rem;display:flex;align-items:center;gap:.5rem}.error-icon[_ngcontent-%COMP%]{font-size:1.2rem}.retry-btn[_ngcontent-%COMP%]{background:#c53030;color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;font-size:.9rem;cursor:pointer;margin-left:auto}.retry-btn[_ngcontent-%COMP%]:hover{background:#9c2626}.workers-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(350px,1fr));gap:1.5rem}.worker-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:1.5rem;box-shadow:0 4px 6px #0000000d;transition:all .2s ease;cursor:pointer;border:2px solid transparent}.worker-card[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 12px 24px #0000001a;border-color:#667eea}.worker-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1rem;margin-bottom:1rem}.worker-avatar[_ngcontent-%COMP%]{width:50px;height:50px;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.2rem;font-weight:700}.worker-info[_ngcontent-%COMP%]{flex:1}.worker-name[_ngcontent-%COMP%]{color:#2d3748;font-size:1.2rem;font-weight:600;margin:0 0 .25rem}.worker-job[_ngcontent-%COMP%]{color:#667eea;font-size:.9rem;font-weight:500;margin:0}.worker-details[_ngcontent-%COMP%]{margin-bottom:1rem}.detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem;color:#4a5568;font-size:.9rem}.detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.icon[_ngcontent-%COMP%]{font-size:1rem}.rating[_ngcontent-%COMP%]{align-items:center}.stars[_ngcontent-%COMP%]{display:flex;gap:.1rem}.star[_ngcontent-%COMP%]{color:gold;font-size:1rem}.rating-text[_ngcontent-%COMP%]{color:#718096;font-size:.8rem;margin-left:.5rem}.worker-description[_ngcontent-%COMP%]{margin-bottom:1rem}.worker-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4a5568;font-size:.9rem;line-height:1.4;margin:0}.worker-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.view-btn[_ngcontent-%COMP%]{background:#667eea;color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;font-size:.9rem;cursor:pointer;transition:all .2s ease}.view-btn[_ngcontent-%COMP%]:hover{background:#5a67d8}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:3rem;background:#fff;border-radius:12px;box-shadow:0 4px 6px #0000000d}.empty-icon[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#2d3748;font-size:1.5rem;margin-bottom:.5rem}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;margin-bottom:1.5rem}@media (max-width: 768px){.worker-list-container[_ngcontent-%COMP%]{padding:1rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.search-row[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:1rem}.workers-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}}@media (max-width: 480px){.worker-list-container[_ngcontent-%COMP%]{padding:.5rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.worker-card[_ngcontent-%COMP%]{padding:1rem}}"]})}}return a})();export{ae as WorkerListComponent};
