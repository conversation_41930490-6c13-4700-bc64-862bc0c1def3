export declare const MODULE_METADATA: {
    IMPORTS: string;
    PROVIDERS: string;
    CONTROLLERS: string;
    EXPORTS: string;
};
export declare const GLOBAL_MODULE_METADATA = "__module:global__";
export declare const HOST_METADATA = "host";
export declare const PATH_METADATA = "path";
export declare const PARAMTYPES_METADATA = "design:paramtypes";
export declare const SELF_DECLARED_DEPS_METADATA = "self:paramtypes";
export declare const OPTIONAL_DEPS_METADATA = "optional:paramtypes";
export declare const PROPERTY_DEPS_METADATA = "self:properties_metadata";
export declare const OPTIONAL_PROPERTY_DEPS_METADATA = "optional:properties_metadata";
export declare const SCOPE_OPTIONS_METADATA = "scope:options";
export declare const METHOD_METADATA = "method";
export declare const ROUTE_ARGS_METADATA = "__routeArguments__";
export declare const CUSTOM_ROUTE_ARGS_METADATA = "__customRouteArgs__";
export declare const FILTER_CATCH_EXCEPTIONS = "__filterCatchExceptions__";
export declare const PIPES_METADATA = "__pipes__";
export declare const GUARDS_METADATA = "__guards__";
export declare const INTERCEPTORS_METADATA = "__interceptors__";
export declare const EXCEPTION_FILTERS_METADATA = "__exceptionFilters__";
export declare const ENHANCER_KEY_TO_SUBTYPE_MAP: {
    readonly __guards__: "guard";
    readonly __interceptors__: "interceptor";
    readonly __pipes__: "pipe";
    readonly __exceptionFilters__: "filter";
};
export type EnhancerSubtype = (typeof ENHANCER_KEY_TO_SUBTYPE_MAP)[keyof typeof ENHANCER_KEY_TO_SUBTYPE_MAP];
export declare const RENDER_METADATA = "__renderTemplate__";
export declare const HTTP_CODE_METADATA = "__httpCode__";
export declare const MODULE_PATH = "__module_path__";
export declare const HEADERS_METADATA = "__headers__";
export declare const REDIRECT_METADATA = "__redirect__";
export declare const RESPONSE_PASSTHROUGH_METADATA = "__responsePassthrough__";
export declare const SSE_METADATA = "__sse__";
export declare const VERSION_METADATA = "__version__";
export declare const INJECTABLE_WATERMARK = "__injectable__";
export declare const CONTROLLER_WATERMARK = "__controller__";
export declare const CATCH_WATERMARK = "__catch__";
export declare const ENTRY_PROVIDER_WATERMARK = "__entryProvider__";
