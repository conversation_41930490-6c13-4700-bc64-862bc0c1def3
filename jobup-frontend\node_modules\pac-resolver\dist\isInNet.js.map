{"version": 3, "file": "isInNet.js", "sourceRoot": "", "sources": ["../src/isInNet.ts"], "names": [], "mappings": ";;AAAA,qCAAkC;AAClC,iCAAmC;AAEnC;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEY,KAAK,UAAU,OAAO,CACpC,IAAY,EACZ,OAAe,EACf,IAAY;IAEZ,MAAM,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI;QACH,MAAM,EAAE,GAAG,MAAM,IAAA,gBAAS,EAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7C,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC3B,MAAM,OAAO,GAAG,IAAI,iBAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAC3C,OAAO,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;SAC5B;KACD;IAAC,OAAO,GAAG,EAAE;QACb,SAAS;KACT;IACD,OAAO,KAAK,CAAC;AACd,CAAC;AAhBD,0BAgBC"}