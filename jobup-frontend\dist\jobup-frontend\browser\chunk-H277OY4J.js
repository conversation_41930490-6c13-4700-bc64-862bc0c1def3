import{$ as mt,$a as tr,A as _e,Aa as Gn,B as $,Ba as Zn,C as ft,D as Dn,Da as Kn,E as On,F as Nn,G as P,H as Pn,I as b,Ia as Qn,J as w,Ka as Yn,L as y,N as Un,O as D,P as pt,Q as C,R as g,Ra as Xn,S as fe,T as ke,U as xn,Va as Fe,W as gt,Wa as Jn,X as vt,Xa as er,Y as Ln,Ya as Rt,Z as $e,Za as bt,_ as q,_a as It,a as h,ab as ze,b as I,bb as Mt,c as bn,cb as nr,d as In,db as rr,e as ct,ea as yt,eb as U,f as ut,fa as jn,fb as Et,g as _,ga as _n,gb as He,h as T,ha as St,hb as ir,i as k,ia as kn,j as N,ja as pe,k as f,ka as $n,l as he,la as Ct,m as Mn,ma as Fn,n as En,na as ge,nb as sr,o as m,oa as wt,ob as Tt,p as lt,pa as zn,pb as or,q as A,qa as ve,r as Tn,s as ht,sa as J,ta as Hn,u as Q,ua as Vn,v as Y,va as j,w as de,wa as Bn,x as dt,y as X,ya as qn,z as An,za as Wn}from"./chunk-T3NICLM4.js";var Ot=class extends rr{constructor(){super(...arguments),this.supportsDOMEvents=!0}},Nt=class t extends Ot{static makeCurrent(){nr(new t)}onAndCancel(n,e,r){return n.addEventListener(e,r),()=>{n.removeEventListener(e,r)}}dispatchEvent(n,e){n.dispatchEvent(e)}remove(n){n.parentNode&&n.parentNode.removeChild(n)}createElement(n,e){return e=e||this.getDefaultDocument(),e.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,e){return e==="window"?window:e==="document"?n:e==="body"?n.body:null}getBaseHref(n){let e=ei();return e==null?null:ti(e)}resetBaseElement(){me=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return ir(document.cookie,n)}},me=null;function ei(){return me=me||document.querySelector("base"),me?me.getAttribute("href"):null}function ti(t){return new URL(t,document.baseURI).pathname}var ni=(()=>{class t{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})(),Pt=new D(""),lr=(()=>{class t{constructor(e,r){this._zone=r,this._eventNameToPlugin=new Map,e.forEach(i=>{i.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,r,i){return this._findPluginFor(r).addEventListener(e,r,i)}getZone(){return this._zone}_findPluginFor(e){let r=this._eventNameToPlugin.get(e);if(r)return r;if(r=this._plugins.find(s=>s.supports(e)),!r)throw new w(5101,!1);return this._eventNameToPlugin.set(e,r),r}static{this.\u0275fac=function(r){return new(r||t)(C(Pt),C(j))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})(),Ve=class{constructor(n){this._doc=n}},At="ng-app-id",hr=(()=>{class t{constructor(e,r,i,s={}){this.doc=e,this.appId=r,this.nonce=i,this.platformId=s,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Tt(s),this.resetHostNodes()}addStyles(e){for(let r of e)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(e){for(let r of e)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let e=this.styleNodesInDOM;e&&(e.forEach(r=>r.remove()),e.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(e){this.hostNodes.add(e);for(let r of this.getAllStyles())this.addStyleToHost(e,r)}removeHost(e){this.hostNodes.delete(e)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(e){for(let r of this.hostNodes)this.addStyleToHost(r,e)}onStyleRemoved(e){let r=this.styleRef;r.get(e)?.elements?.forEach(i=>i.remove()),r.delete(e)}collectServerRenderedStyles(){let e=this.doc.head?.querySelectorAll(`style[${At}="${this.appId}"]`);if(e?.length){let r=new Map;return e.forEach(i=>{i.textContent!=null&&r.set(i.textContent,i)}),r}return null}changeUsageCount(e,r){let i=this.styleRef;if(i.has(e)){let s=i.get(e);return s.usage+=r,s.usage}return i.set(e,{usage:r,elements:[]}),r}getStyleElement(e,r){let i=this.styleNodesInDOM,s=i?.get(r);if(s?.parentNode===e)return i.delete(r),s.removeAttribute(At),s;{let o=this.doc.createElement("style");return this.nonce&&o.setAttribute("nonce",this.nonce),o.textContent=r,this.platformIsServer&&o.setAttribute(At,this.appId),e.appendChild(o),o}}addStyleToHost(e,r){let i=this.getStyleElement(e,r),s=this.styleRef,o=s.get(r)?.elements;o?o.push(i):s.set(r,{elements:[i],usage:1})}resetHostNodes(){let e=this.hostNodes;e.clear(),e.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||t)(C(U),C(Ct),C(wt,8),C(ge))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})(),Dt={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},xt=/%COMP%/g,dr="%COMP%",ri=`_nghost-${dr}`,ii=`_ngcontent-${dr}`,si=!0,oi=new D("",{providedIn:"root",factory:()=>si});function ai(t){return ii.replace(xt,t)}function ci(t){return ri.replace(xt,t)}function fr(t,n){return n.map(e=>e.replace(xt,t))}var ar=(()=>{class t{constructor(e,r,i,s,o,c,a,u=null){this.eventManager=e,this.sharedStylesHost=r,this.appId=i,this.removeStylesOnCompDestroy=s,this.doc=o,this.platformId=c,this.ngZone=a,this.nonce=u,this.rendererByCompId=new Map,this.platformIsServer=Tt(c),this.defaultRenderer=new ye(e,o,a,this.platformIsServer)}createRenderer(e,r){if(!e||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===fe.ShadowDom&&(r=I(h({},r),{encapsulation:fe.Emulated}));let i=this.getOrCreateRenderer(e,r);return i instanceof Be?i.applyToHost(e):i instanceof Se&&i.applyStyles(),i}getOrCreateRenderer(e,r){let i=this.rendererByCompId,s=i.get(r.id);if(!s){let o=this.doc,c=this.ngZone,a=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case fe.Emulated:s=new Be(a,u,r,this.appId,l,o,c,d);break;case fe.ShadowDom:return new Ut(a,u,e,r,o,c,this.nonce,d);default:s=new Se(a,u,r,l,o,c,d);break}i.set(r.id,s)}return s}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||t)(C(lr),C(hr),C(Ct),C(oi),C(U),C(ge),C(j),C(wt))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})(),ye=class{constructor(n,e,r,i){this.eventManager=n,this.doc=e,this.ngZone=r,this.platformIsServer=i,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(n,e){return e?this.doc.createElementNS(Dt[e]||e,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,e){(cr(n)?n.content:n).appendChild(e)}insertBefore(n,e,r){n&&(cr(n)?n.content:n).insertBefore(e,r)}removeChild(n,e){n&&n.removeChild(e)}selectRootElement(n,e){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new w(-5104,!1);return e||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,e,r,i){if(i){e=i+":"+e;let s=Dt[i];s?n.setAttributeNS(s,e,r):n.setAttribute(e,r)}else n.setAttribute(e,r)}removeAttribute(n,e,r){if(r){let i=Dt[r];i?n.removeAttributeNS(i,e):n.removeAttribute(`${r}:${e}`)}else n.removeAttribute(e)}addClass(n,e){n.classList.add(e)}removeClass(n,e){n.classList.remove(e)}setStyle(n,e,r,i){i&(ve.DashCase|ve.Important)?n.style.setProperty(e,r,i&ve.Important?"important":""):n.style[e]=r}removeStyle(n,e,r){r&ve.DashCase?n.style.removeProperty(e):n.style[e]=""}setProperty(n,e,r){n!=null&&(n[e]=r)}setValue(n,e){n.nodeValue=e}listen(n,e,r){if(typeof n=="string"&&(n=Mt().getGlobalEventTarget(this.doc,n),!n))throw new Error(`Unsupported event target ${n} for event ${e}`);return this.eventManager.addEventListener(n,e,this.decoratePreventDefault(r))}decoratePreventDefault(n){return e=>{if(e==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(e)):n(e))===!1&&e.preventDefault()}}};function cr(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var Ut=class extends ye{constructor(n,e,r,i,s,o,c,a){super(n,s,o,a),this.sharedStylesHost=e,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=fr(i.id,i.styles);for(let l of u){let d=document.createElement("style");c&&d.setAttribute("nonce",c),d.textContent=l,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,e){return super.appendChild(this.nodeOrShadowRoot(n),e)}insertBefore(n,e,r){return super.insertBefore(this.nodeOrShadowRoot(n),e,r)}removeChild(n,e){return super.removeChild(this.nodeOrShadowRoot(n),e)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Se=class extends ye{constructor(n,e,r,i,s,o,c,a){super(n,s,o,c),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=i,this.styles=a?fr(a,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Be=class extends Se{constructor(n,e,r,i,s,o,c,a){let u=i+"-"+r.id;super(n,e,r,s,o,c,a,u),this.contentAttr=ai(u),this.hostAttr=ci(u)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,e){let r=super.createElement(n,e);return super.setAttribute(r,this.contentAttr,""),r}},ui=(()=>{class t extends Ve{constructor(e){super(e)}supports(e){return!0}addEventListener(e,r,i){return e.addEventListener(r,i,!1),()=>this.removeEventListener(e,r,i)}removeEventListener(e,r,i){return e.removeEventListener(r,i)}static{this.\u0275fac=function(r){return new(r||t)(C(U))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})(),ur=["alt","control","meta","shift"],li={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},hi={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},di=(()=>{class t extends Ve{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,r,i){let s=t.parseEventName(r),o=t.eventCallback(s.fullKey,i,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Mt().onAndCancel(e,s.domEventName,o))}static parseEventName(e){let r=e.toLowerCase().split("."),i=r.shift();if(r.length===0||!(i==="keydown"||i==="keyup"))return null;let s=t._normalizeKey(r.pop()),o="",c=r.indexOf("code");if(c>-1&&(r.splice(c,1),o="code."),ur.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),o+=u+".")}),o+=s,r.length!=0||s.length===0)return null;let a={};return a.domEventName=i,a.fullKey=o,a}static matchEventFullKeyCode(e,r){let i=li[e.key]||e.key,s="";return r.indexOf("code.")>-1&&(i=e.code,s="code."),i==null||!i?!1:(i=i.toLowerCase(),i===" "?i="space":i==="."&&(i="dot"),ur.forEach(o=>{if(o!==i){let c=hi[o];c(e)&&(s+=o+".")}}),s+=i,s===r)}static eventCallback(e,r,i){return s=>{t.matchEventFullKeyCode(s,e)&&i.runGuarded(()=>r(s))}}static _normalizeKey(e){return e==="esc"?"escape":e}static{this.\u0275fac=function(r){return new(r||t)(C(U))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac})}}return t})();function Bo(t,n){return tr(h({rootComponent:t},fi(n)))}function fi(t){return{appProviders:[...yi,...t?.providers??[]],platformProviders:mi}}function pi(){Nt.makeCurrent()}function gi(){return new St}function vi(){return $n(document),document}var mi=[{provide:ge,useValue:sr},{provide:Fn,useValue:pi,multi:!0},{provide:U,useFactory:vi,deps:[]}];var yi=[{provide:Ln,useValue:"root"},{provide:St,useFactory:gi,deps:[]},{provide:Pt,useClass:ui,multi:!0,deps:[U,j,ge]},{provide:Pt,useClass:di,multi:!0,deps:[U]},ar,hr,lr,{provide:Hn,useExisting:ar},{provide:or,useClass:ni,deps:[]},[]];var pr=(()=>{class t{constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static{this.\u0275fac=function(r){return new(r||t)(C(U))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var p="primary",xe=Symbol("RouteTitle"),$t=class{constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e[0]:e}return null}getAll(n){if(this.has(n)){let e=this.params[n];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function ie(t){return new $t(t)}function Ci(t,n,e){let r=e.path.split("/");if(r.length>t.length||e.pathMatch==="full"&&(n.hasChildren()||r.length<t.length))return null;let i={};for(let s=0;s<r.length;s++){let o=r[s],c=t[s];if(o.startsWith(":"))i[o.substring(1)]=c;else if(o!==c.path)return null}return{consumed:t.slice(0,r.length),posParams:i}}function wi(t,n){if(t.length!==n.length)return!1;for(let e=0;e<t.length;++e)if(!x(t[e],n[e]))return!1;return!0}function x(t,n){let e=t?Ft(t):void 0,r=n?Ft(n):void 0;if(!e||!r||e.length!=r.length)return!1;let i;for(let s=0;s<e.length;s++)if(i=e[s],!Cr(t[i],n[i]))return!1;return!0}function Ft(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Cr(t,n){if(Array.isArray(t)&&Array.isArray(n)){if(t.length!==n.length)return!1;let e=[...t].sort(),r=[...n].sort();return e.every((i,s)=>r[s]===i)}else return t===n}function wr(t){return t.length>0?t[t.length-1]:null}function V(t){return Mn(t)?t:Jn(t)?N(Promise.resolve(t)):f(t)}var Ri={exact:br,subset:Ir},Rr={exact:bi,subset:Ii,ignored:()=>!0};function gr(t,n,e){return Ri[e.paths](t.root,n.root,e.matrixParams)&&Rr[e.queryParams](t.queryParams,n.queryParams)&&!(e.fragment==="exact"&&t.fragment!==n.fragment)}function bi(t,n){return x(t,n)}function br(t,n,e){if(!G(t.segments,n.segments)||!Ge(t.segments,n.segments,e)||t.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!t.children[r]||!br(t.children[r],n.children[r],e))return!1;return!0}function Ii(t,n){return Object.keys(n).length<=Object.keys(t).length&&Object.keys(n).every(e=>Cr(t[e],n[e]))}function Ir(t,n,e){return Mr(t,n,n.segments,e)}function Mr(t,n,e,r){if(t.segments.length>e.length){let i=t.segments.slice(0,e.length);return!(!G(i,e)||n.hasChildren()||!Ge(i,e,r))}else if(t.segments.length===e.length){if(!G(t.segments,e)||!Ge(t.segments,e,r))return!1;for(let i in n.children)if(!t.children[i]||!Ir(t.children[i],n.children[i],r))return!1;return!0}else{let i=e.slice(0,t.segments.length),s=e.slice(t.segments.length);return!G(t.segments,i)||!Ge(t.segments,i,r)||!t.children[p]?!1:Mr(t.children[p],n,s,r)}}function Ge(t,n,e){return n.every((r,i)=>Rr[e](t[i].parameters,r.parameters))}var F=class{constructor(n=new v([],{}),e={},r=null){this.root=n,this.queryParams=e,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ie(this.queryParams),this._queryParamMap}toString(){return Ti.serialize(this)}},v=class{constructor(n,e){this.segments=n,this.children=e,this.parent=null,Object.values(e).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ze(this)}},W=class{constructor(n,e){this.path=n,this.parameters=e}get parameterMap(){return this._parameterMap??=ie(this.parameters),this._parameterMap}toString(){return Tr(this)}};function Mi(t,n){return G(t,n)&&t.every((e,r)=>x(e.parameters,n[r].parameters))}function G(t,n){return t.length!==n.length?!1:t.every((e,r)=>e.path===n[r].path)}function Ei(t,n){let e=[];return Object.entries(t.children).forEach(([r,i])=>{r===p&&(e=e.concat(n(i,r)))}),Object.entries(t.children).forEach(([r,i])=>{r!==p&&(e=e.concat(n(i,r)))}),e}var fn=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:()=>new Qe,providedIn:"root"})}}return t})(),Qe=class{parse(n){let e=new Ht(n);return new F(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(n){let e=`/${Ce(n.root,!0)}`,r=Oi(n.queryParams),i=typeof n.fragment=="string"?`#${Ai(n.fragment)}`:"";return`${e}${r}${i}`}},Ti=new Qe;function Ze(t){return t.segments.map(n=>Tr(n)).join("/")}function Ce(t,n){if(!t.hasChildren())return Ze(t);if(n){let e=t.children[p]?Ce(t.children[p],!1):"",r=[];return Object.entries(t.children).forEach(([i,s])=>{i!==p&&r.push(`${i}:${Ce(s,!1)}`)}),r.length>0?`${e}(${r.join("//")})`:e}else{let e=Ei(t,(r,i)=>i===p?[Ce(t.children[p],!1)]:[`${i}:${Ce(r,!1)}`]);return Object.keys(t.children).length===1&&t.children[p]!=null?`${Ze(t)}/${e[0]}`:`${Ze(t)}/(${e.join("//")})`}}function Er(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function qe(t){return Er(t).replace(/%3B/gi,";")}function Ai(t){return encodeURI(t)}function zt(t){return Er(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Ke(t){return decodeURIComponent(t)}function vr(t){return Ke(t.replace(/\+/g,"%20"))}function Tr(t){return`${zt(t.path)}${Di(t.parameters)}`}function Di(t){return Object.entries(t).map(([n,e])=>`;${zt(n)}=${zt(e)}`).join("")}function Oi(t){let n=Object.entries(t).map(([e,r])=>Array.isArray(r)?r.map(i=>`${qe(e)}=${qe(i)}`).join("&"):`${qe(e)}=${qe(r)}`).filter(e=>e);return n.length?`?${n.join("&")}`:""}var Ni=/^[^\/()?;#]+/;function Lt(t){let n=t.match(Ni);return n?n[0]:""}var Pi=/^[^\/()?;=#]+/;function Ui(t){let n=t.match(Pi);return n?n[0]:""}var xi=/^[^=?&#]+/;function Li(t){let n=t.match(xi);return n?n[0]:""}var ji=/^[^&#]+/;function _i(t){let n=t.match(ji);return n?n[0]:""}var Ht=class{constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new v([],{}):new v([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(e).length>0)&&(r[p]=new v(n,e)),r}parseSegment(){let n=Lt(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new w(4009,!1);return this.capture(n),new W(Ke(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let e=Ui(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let i=Lt(this.remaining);i&&(r=i,this.capture(r))}n[Ke(e)]=Ke(r)}parseQueryParam(n){let e=Li(this.remaining);if(!e)return;this.capture(e);let r="";if(this.consumeOptional("=")){let o=_i(this.remaining);o&&(r=o,this.capture(r))}let i=vr(e),s=vr(r);if(n.hasOwnProperty(i)){let o=n[i];Array.isArray(o)||(o=[o],n[i]=o),o.push(s)}else n[i]=s}parseParens(n){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Lt(this.remaining),i=this.remaining[r.length];if(i!=="/"&&i!==")"&&i!==";")throw new w(4010,!1);let s;r.indexOf(":")>-1?(s=r.slice(0,r.indexOf(":")),this.capture(s),this.capture(":")):n&&(s=p);let o=this.parseChildren();e[s]=Object.keys(o).length===1?o[p]:new v([],o),this.consumeOptional("//")}return e}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new w(4011,!1)}};function Ar(t){return t.segments.length>0?new v([],{[p]:t}):t}function Dr(t){let n={};for(let[r,i]of Object.entries(t.children)){let s=Dr(i);if(r===p&&s.segments.length===0&&s.hasChildren())for(let[o,c]of Object.entries(s.children))n[o]=c;else(s.segments.length>0||s.hasChildren())&&(n[r]=s)}let e=new v(t.segments,n);return ki(e)}function ki(t){if(t.numberOfChildren===1&&t.children[p]){let n=t.children[p];return new v(t.segments.concat(n.segments),n.children)}return t}function se(t){return t instanceof F}function $i(t,n,e=null,r=null){let i=Or(t);return Nr(i,n,e,r)}function Or(t){let n;function e(s){let o={};for(let a of s.children){let u=e(a);o[a.outlet]=u}let c=new v(s.url,o);return s===t&&(n=c),c}let r=e(t.root),i=Ar(r);return n??i}function Nr(t,n,e,r){let i=t;for(;i.parent;)i=i.parent;if(n.length===0)return jt(i,i,i,e,r);let s=Fi(n);if(s.toRoot())return jt(i,i,new v([],{}),e,r);let o=zi(s,i,t),c=o.processChildren?be(o.segmentGroup,o.index,s.commands):Ur(o.segmentGroup,o.index,s.commands);return jt(i,o.segmentGroup,c,e,r)}function Ye(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function Ee(t){return typeof t=="object"&&t!=null&&t.outlets}function jt(t,n,e,r,i){let s={};r&&Object.entries(r).forEach(([a,u])=>{s[a]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let o;t===n?o=e:o=Pr(t,n,e);let c=Ar(Dr(o));return new F(c,s,i)}function Pr(t,n,e){let r={};return Object.entries(t.children).forEach(([i,s])=>{s===n?r[i]=e:r[i]=Pr(s,n,e)}),new v(t.segments,r)}var Xe=class{constructor(n,e,r){if(this.isAbsolute=n,this.numberOfDoubleDots=e,this.commands=r,n&&r.length>0&&Ye(r[0]))throw new w(4003,!1);let i=r.find(Ee);if(i&&i!==wr(r))throw new w(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Fi(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new Xe(!0,0,t);let n=0,e=!1,r=t.reduce((i,s,o)=>{if(typeof s=="object"&&s!=null){if(s.outlets){let c={};return Object.entries(s.outlets).forEach(([a,u])=>{c[a]=typeof u=="string"?u.split("/"):u}),[...i,{outlets:c}]}if(s.segmentPath)return[...i,s.segmentPath]}return typeof s!="string"?[...i,s]:o===0?(s.split("/").forEach((c,a)=>{a==0&&c==="."||(a==0&&c===""?e=!0:c===".."?n++:c!=""&&i.push(c))}),i):[...i,s]},[]);return new Xe(e,n,r)}var ne=class{constructor(n,e,r){this.segmentGroup=n,this.processChildren=e,this.index=r}};function zi(t,n,e){if(t.isAbsolute)return new ne(n,!0,0);if(!e)return new ne(n,!1,NaN);if(e.parent===null)return new ne(e,!0,0);let r=Ye(t.commands[0])?0:1,i=e.segments.length-1+r;return Hi(e,i,t.numberOfDoubleDots)}function Hi(t,n,e){let r=t,i=n,s=e;for(;s>i;){if(s-=i,r=r.parent,!r)throw new w(4005,!1);i=r.segments.length}return new ne(r,!1,i-s)}function Vi(t){return Ee(t[0])?t[0].outlets:{[p]:t}}function Ur(t,n,e){if(t??=new v([],{}),t.segments.length===0&&t.hasChildren())return be(t,n,e);let r=Bi(t,n,e),i=e.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){let s=new v(t.segments.slice(0,r.pathIndex),{});return s.children[p]=new v(t.segments.slice(r.pathIndex),t.children),be(s,0,i)}else return r.match&&i.length===0?new v(t.segments,{}):r.match&&!t.hasChildren()?Vt(t,n,e):r.match?be(t,0,i):Vt(t,n,e)}function be(t,n,e){if(e.length===0)return new v(t.segments,{});{let r=Vi(e),i={};if(Object.keys(r).some(s=>s!==p)&&t.children[p]&&t.numberOfChildren===1&&t.children[p].segments.length===0){let s=be(t.children[p],n,e);return new v(t.segments,s.children)}return Object.entries(r).forEach(([s,o])=>{typeof o=="string"&&(o=[o]),o!==null&&(i[s]=Ur(t.children[s],n,o))}),Object.entries(t.children).forEach(([s,o])=>{r[s]===void 0&&(i[s]=o)}),new v(t.segments,i)}}function Bi(t,n,e){let r=0,i=n,s={match:!1,pathIndex:0,commandIndex:0};for(;i<t.segments.length;){if(r>=e.length)return s;let o=t.segments[i],c=e[r];if(Ee(c))break;let a=`${c}`,u=r<e.length-1?e[r+1]:null;if(i>0&&a===void 0)break;if(a&&u&&typeof u=="object"&&u.outlets===void 0){if(!yr(a,u,o))return s;r+=2}else{if(!yr(a,{},o))return s;r++}i++}return{match:!0,pathIndex:i,commandIndex:r}}function Vt(t,n,e){let r=t.segments.slice(0,n),i=0;for(;i<e.length;){let s=e[i];if(Ee(s)){let a=qi(s.outlets);return new v(r,a)}if(i===0&&Ye(e[0])){let a=t.segments[n];r.push(new W(a.path,mr(e[0]))),i++;continue}let o=Ee(s)?s.outlets[p]:`${s}`,c=i<e.length-1?e[i+1]:null;o&&c&&Ye(c)?(r.push(new W(o,mr(c))),i+=2):(r.push(new W(o,{})),i++)}return new v(r,{})}function qi(t){let n={};return Object.entries(t).forEach(([e,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[e]=Vt(new v([],{}),0,r))}),n}function mr(t){let n={};return Object.entries(t).forEach(([e,r])=>n[e]=`${r}`),n}function yr(t,n,e){return t==e.path&&x(n,e.parameters)}var Ie="imperative",R=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(R||{}),O=class{constructor(n,e){this.id=n,this.url=e}},Te=class extends O{constructor(n,e,r="imperative",i=null){super(n,e),this.type=R.NavigationStart,this.navigationTrigger=r,this.restoredState=i}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},z=class extends O{constructor(n,e,r){super(n,e),this.urlAfterRedirects=r,this.type=R.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},E=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(E||{}),Bt=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(Bt||{}),H=class extends O{constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i,this.type=R.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Z=class extends O{constructor(n,e,r,i){super(n,e),this.reason=r,this.code=i,this.type=R.NavigationSkipped}},Ae=class extends O{constructor(n,e,r,i){super(n,e),this.error=r,this.target=i,this.type=R.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Je=class extends O{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=R.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},qt=class extends O{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=R.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Wt=class extends O{constructor(n,e,r,i,s){super(n,e),this.urlAfterRedirects=r,this.state=i,this.shouldActivate=s,this.type=R.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Gt=class extends O{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=R.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Zt=class extends O{constructor(n,e,r,i){super(n,e),this.urlAfterRedirects=r,this.state=i,this.type=R.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Kt=class{constructor(n){this.route=n,this.type=R.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Qt=class{constructor(n){this.route=n,this.type=R.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Yt=class{constructor(n){this.snapshot=n,this.type=R.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Xt=class{constructor(n){this.snapshot=n,this.type=R.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Jt=class{constructor(n){this.snapshot=n,this.type=R.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},en=class{constructor(n){this.snapshot=n,this.type=R.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var De=class{},Oe=class{constructor(n){this.url=n}};var tn=class{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new st,this.attachRef=null}},st=(()=>{class t{constructor(){this.contexts=new Map}onChildOutletCreated(e,r){let i=this.getOrCreateContext(e);i.outlet=r,this.contexts.set(e,i)}onChildOutletDestroyed(e){let r=this.getContext(e);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let r=this.getContext(e);return r||(r=new tn,this.contexts.set(e,r)),r}getContext(e){return this.contexts.get(e)||null}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),et=class{constructor(n){this._root=n}get root(){return this._root.value}parent(n){let e=this.pathFromRoot(n);return e.length>1?e[e.length-2]:null}children(n){let e=nn(n,this._root);return e?e.children.map(r=>r.value):[]}firstChild(n){let e=nn(n,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(n){let e=rn(n,this._root);return e.length<2?[]:e[e.length-2].children.map(i=>i.value).filter(i=>i!==n)}pathFromRoot(n){return rn(n,this._root).map(e=>e.value)}};function nn(t,n){if(t===n.value)return n;for(let e of n.children){let r=nn(t,e);if(r)return r}return null}function rn(t,n){if(t===n.value)return[n];for(let e of n.children){let r=rn(t,e);if(r.length)return r.unshift(n),r}return[]}var M=class{constructor(n,e){this.value=n,this.children=e}toString(){return`TreeNode(${this.value})`}};function te(t){let n={};return t&&t.children.forEach(e=>n[e.value.outlet]=e),n}var tt=class extends et{constructor(n,e){super(n),this.snapshot=e,gn(this,n)}toString(){return this.snapshot.toString()}};function xr(t){let n=Wi(t),e=new T([new W("",{})]),r=new T({}),i=new T({}),s=new T({}),o=new T(""),c=new K(e,r,s,o,i,p,t,n.root);return c.snapshot=n.root,new tt(new M(c,[]),n)}function Wi(t){let n={},e={},r={},i="",s=new Ne([],n,r,i,e,p,t,null,{});return new nt("",new M(s,[]))}var K=class{constructor(n,e,r,i,s,o,c,a){this.urlSubject=n,this.paramsSubject=e,this.queryParamsSubject=r,this.fragmentSubject=i,this.dataSubject=s,this.outlet=o,this.component=c,this._futureSnapshot=a,this.title=this.dataSubject?.pipe(m(u=>u[xe]))??f(void 0),this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(m(n=>ie(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(m(n=>ie(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function pn(t,n,e="emptyOnly"){let r,{routeConfig:i}=t;return n!==null&&(e==="always"||i?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:h(h({},n.params),t.params),data:h(h({},n.data),t.data),resolve:h(h(h(h({},t.data),n.data),i?.data),t._resolvedData)}:r={params:h({},t.params),data:h({},t.data),resolve:h(h({},t.data),t._resolvedData??{})},i&&jr(i)&&(r.resolve[xe]=i.title),r}var Ne=class{get title(){return this.data?.[xe]}constructor(n,e,r,i,s,o,c,a,u){this.url=n,this.params=e,this.queryParams=r,this.fragment=i,this.data=s,this.outlet=o,this.component=c,this.routeConfig=a,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ie(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ie(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${e}')`}},nt=class extends et{constructor(n,e){super(e),this.url=n,gn(this,e)}toString(){return Lr(this._root)}};function gn(t,n){n.value._routerState=t,n.children.forEach(e=>gn(t,e))}function Lr(t){let n=t.children.length>0?` { ${t.children.map(Lr).join(", ")} } `:"";return`${t.value}${n}`}function _t(t){if(t.snapshot){let n=t.snapshot,e=t._futureSnapshot;t.snapshot=e,x(n.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),n.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),x(n.params,e.params)||t.paramsSubject.next(e.params),wi(n.url,e.url)||t.urlSubject.next(e.url),x(n.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function sn(t,n){let e=x(t.params,n.params)&&Mi(t.url,n.url),r=!t.parent!=!n.parent;return e&&!r&&(!t.parent||sn(t.parent,n.parent))}function jr(t){return typeof t.title=="string"||t.title===null}var Gi=(()=>{class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=p,this.activateEvents=new pe,this.deactivateEvents=new pe,this.attachEvents=new pe,this.detachEvents=new pe,this.parentContexts=g(st),this.location=g(Bn),this.changeDetector=g(It),this.environmentInjector=g($e),this.inputBinder=g(vn,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(e){if(e.name){let{firstChange:r,previousValue:i}=e.name;if(r)return;this.isTrackedInParentContexts(i)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(i)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new w(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new w(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new w(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,r){this.activated=e,this._activatedRoute=r,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,r){if(this.isActivated)throw new w(4013,!1);this._activatedRoute=e;let i=this.location,o=e.snapshot.component,c=this.parentContexts.getOrCreateContext(this.name).children,a=new on(e,c,i.injector);this.activated=i.createComponent(o,{index:i.length,injector:a,environmentInjector:r??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275dir=gt({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[mt]})}}return t})(),on=class t{__ngOutletInjector(n){return new t(this.route,this.childContexts,n)}constructor(n,e,r){this.route=n,this.childContexts=e,this.parent=r}get(n,e){return n===K?this.route:n===st?this.childContexts:this.parent.get(n,e)}},vn=new D("");function Zi(t,n,e){let r=Pe(t,n._root,e?e._root:void 0);return new tt(r,n)}function Pe(t,n,e){if(e&&t.shouldReuseRoute(n.value,e.value.snapshot)){let r=e.value;r._futureSnapshot=n.value;let i=Ki(t,n,e);return new M(r,i)}else{if(t.shouldAttach(n.value)){let s=t.retrieve(n.value);if(s!==null){let o=s.route;return o.value._futureSnapshot=n.value,o.children=n.children.map(c=>Pe(t,c)),o}}let r=Qi(n.value),i=n.children.map(s=>Pe(t,s));return new M(r,i)}}function Ki(t,n,e){return n.children.map(r=>{for(let i of e.children)if(t.shouldReuseRoute(r.value,i.value.snapshot))return Pe(t,r,i);return Pe(t,r)})}function Qi(t){return new K(new T(t.url),new T(t.params),new T(t.queryParams),new T(t.fragment),new T(t.data),t.outlet,t.component,t)}var _r="ngNavigationCancelingError";function kr(t,n){let{redirectTo:e,navigationBehaviorOptions:r}=se(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,i=$r(!1,E.Redirect);return i.url=e,i.navigationBehaviorOptions=r,i}function $r(t,n){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[_r]=!0,e.cancellationCode=n,e}function Yi(t){return Fr(t)&&se(t.url)}function Fr(t){return!!t&&t[_r]}var Xi=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275cmp=xn({type:t,selectors:[["ng-component"]],standalone:!0,features:[Xn],decls:1,vars:0,template:function(r,i){r&1&&Qn(0,"router-outlet")},dependencies:[Gi],encapsulation:2})}}return t})();function Ji(t,n){return t.providers&&!t._injector&&(t._injector=Gn(t.providers,n,`Route: ${t.path}`)),t._injector??n}function mn(t){let n=t.children&&t.children.map(mn),e=n?I(h({},t),{children:n}):h({},t);return!e.component&&!e.loadComponent&&(n||e.loadChildren)&&e.outlet&&e.outlet!==p&&(e.component=Xi),e}function L(t){return t.outlet||p}function es(t,n){let e=t.filter(r=>L(r)===n);return e.push(...t.filter(r=>L(r)!==n)),e}function Le(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let n=t.parent;n;n=n.parent){let e=n.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var ts=(t,n,e,r)=>m(i=>(new an(n,i.targetRouterState,i.currentRouterState,e,r).activate(t),i)),an=class{constructor(n,e,r,i,s){this.routeReuseStrategy=n,this.futureState=e,this.currState=r,this.forwardEvent=i,this.inputBindingEnabled=s}activate(n){let e=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,r,n),_t(this.futureState.root),this.activateChildRoutes(e,r,n)}deactivateChildRoutes(n,e,r){let i=te(e);n.children.forEach(s=>{let o=s.value.outlet;this.deactivateRoutes(s,i[o],r),delete i[o]}),Object.values(i).forEach(s=>{this.deactivateRouteAndItsChildren(s,r)})}deactivateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(i===s)if(i.component){let o=r.getContext(i.outlet);o&&this.deactivateChildRoutes(n,e,o.children)}else this.deactivateChildRoutes(n,e,r);else s&&this.deactivateRouteAndItsChildren(e,r)}deactivateRouteAndItsChildren(n,e){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,e):this.deactivateRouteAndOutlet(n,e)}detachAndStoreRouteSubtree(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=te(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);if(r&&r.outlet){let o=r.outlet.detach(),c=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:o,route:n,contexts:c})}}deactivateRouteAndOutlet(n,e){let r=e.getContext(n.value.outlet),i=r&&n.value.component?r.children:e,s=te(n);for(let o of Object.values(s))this.deactivateRouteAndItsChildren(o,i);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,e,r){let i=te(e);n.children.forEach(s=>{this.activateRoutes(s,i[s.value.outlet],r),this.forwardEvent(new en(s.value.snapshot))}),n.children.length&&this.forwardEvent(new Xt(n.value.snapshot))}activateRoutes(n,e,r){let i=n.value,s=e?e.value:null;if(_t(i),i===s)if(i.component){let o=r.getOrCreateContext(i.outlet);this.activateChildRoutes(n,e,o.children)}else this.activateChildRoutes(n,e,r);else if(i.component){let o=r.getOrCreateContext(i.outlet);if(this.routeReuseStrategy.shouldAttach(i.snapshot)){let c=this.routeReuseStrategy.retrieve(i.snapshot);this.routeReuseStrategy.store(i.snapshot,null),o.children.onOutletReAttached(c.contexts),o.attachRef=c.componentRef,o.route=c.route.value,o.outlet&&o.outlet.attach(c.componentRef,c.route.value),_t(c.route.value),this.activateChildRoutes(n,null,o.children)}else{let c=Le(i.snapshot);o.attachRef=null,o.route=i,o.injector=c,o.outlet&&o.outlet.activateWith(i,o.injector),this.activateChildRoutes(n,null,o.children)}}else this.activateChildRoutes(n,null,r)}},rt=class{constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},re=class{constructor(n,e){this.component=n,this.route=e}};function ns(t,n,e){let r=t._root,i=n?n._root:null;return we(r,i,e,[r.value])}function rs(t){let n=t.routeConfig?t.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:t,guards:n}}function ae(t,n){let e=Symbol(),r=n.get(t,e);return r===e?typeof t=="function"&&!Un(t)?t:n.get(t):r}function we(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=te(n);return t.children.forEach(o=>{is(o,s[o.value.outlet],e,r.concat([o.value]),i),delete s[o.value.outlet]}),Object.entries(s).forEach(([o,c])=>Me(c,e.getContext(o),i)),i}function is(t,n,e,r,i={canDeactivateChecks:[],canActivateChecks:[]}){let s=t.value,o=n?n.value:null,c=e?e.getContext(t.value.outlet):null;if(o&&s.routeConfig===o.routeConfig){let a=ss(o,s,s.routeConfig.runGuardsAndResolvers);a?i.canActivateChecks.push(new rt(r)):(s.data=o.data,s._resolvedData=o._resolvedData),s.component?we(t,n,c?c.children:null,r,i):we(t,n,e,r,i),a&&c&&c.outlet&&c.outlet.isActivated&&i.canDeactivateChecks.push(new re(c.outlet.component,o))}else o&&Me(n,c,i),i.canActivateChecks.push(new rt(r)),s.component?we(t,null,c?c.children:null,r,i):we(t,null,e,r,i);return i}function ss(t,n,e){if(typeof e=="function")return e(t,n);switch(e){case"pathParamsChange":return!G(t.url,n.url);case"pathParamsOrQueryParamsChange":return!G(t.url,n.url)||!x(t.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!sn(t,n)||!x(t.queryParams,n.queryParams);case"paramsChange":default:return!sn(t,n)}}function Me(t,n,e){let r=te(t),i=t.value;Object.entries(r).forEach(([s,o])=>{i.component?n?Me(o,n.children.getContext(s),e):Me(o,null,e):Me(o,n,e)}),i.component?n&&n.outlet&&n.outlet.isActivated?e.canDeactivateChecks.push(new re(n.outlet.component,i)):e.canDeactivateChecks.push(new re(null,i)):e.canDeactivateChecks.push(new re(null,i))}function je(t){return typeof t=="function"}function os(t){return typeof t=="boolean"}function as(t){return t&&je(t.canLoad)}function cs(t){return t&&je(t.canActivate)}function us(t){return t&&je(t.canActivateChild)}function ls(t){return t&&je(t.canDeactivate)}function hs(t){return t&&je(t.canMatch)}function zr(t){return t instanceof En||t?.name==="EmptyError"}var We=Symbol("INITIAL_VALUE");function oe(){return P(t=>lt(t.map(n=>n.pipe(X(1),Nn(We)))).pipe(m(n=>{for(let e of n)if(e!==!0){if(e===We)return We;if(e===!1||e instanceof F)return e}return!0}),Q(n=>n!==We),X(1)))}function ds(t,n){return A(e=>{let{targetSnapshot:r,currentSnapshot:i,guards:{canActivateChecks:s,canDeactivateChecks:o}}=e;return o.length===0&&s.length===0?f(I(h({},e),{guardsResult:!0})):fs(o,r,i,t).pipe(A(c=>c&&os(c)?ps(r,s,t,n):f(c)),m(c=>I(h({},e),{guardsResult:c})))})}function fs(t,n,e,r){return N(t).pipe(A(i=>Ss(i.component,i.route,e,n,r)),$(i=>i!==!0,!0))}function ps(t,n,e,r){return N(n).pipe(de(i=>Tn(vs(i.route.parent,r),gs(i.route,r),ys(t,i.path,e),ms(t,i.route,e))),$(i=>i!==!0,!0))}function gs(t,n){return t!==null&&n&&n(new Jt(t)),f(!0)}function vs(t,n){return t!==null&&n&&n(new Yt(t)),f(!0)}function ms(t,n,e){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return f(!0);let i=r.map(s=>ht(()=>{let o=Le(n)??e,c=ae(s,o),a=cs(c)?c.canActivate(n,t):q(o,()=>c(n,t));return V(a).pipe($())}));return f(i).pipe(oe())}function ys(t,n,e){let r=n[n.length-1],s=n.slice(0,n.length-1).reverse().map(o=>rs(o)).filter(o=>o!==null).map(o=>ht(()=>{let c=o.guards.map(a=>{let u=Le(o.node)??e,l=ae(a,u),d=us(l)?l.canActivateChild(r,t):q(u,()=>l(r,t));return V(d).pipe($())});return f(c).pipe(oe())}));return f(s).pipe(oe())}function Ss(t,n,e,r,i){let s=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!s||s.length===0)return f(!0);let o=s.map(c=>{let a=Le(n)??i,u=ae(c,a),l=ls(u)?u.canDeactivate(t,n,e,r):q(a,()=>u(t,n,e,r));return V(l).pipe($())});return f(o).pipe(oe())}function Cs(t,n,e,r){let i=n.canLoad;if(i===void 0||i.length===0)return f(!0);let s=i.map(o=>{let c=ae(o,t),a=as(c)?c.canLoad(n,e):q(t,()=>c(n,e));return V(a)});return f(s).pipe(oe(),Hr(r))}function Hr(t){return In(b(n=>{if(se(n))throw kr(t,n)}),m(n=>n===!0))}function ws(t,n,e,r){let i=n.canMatch;if(!i||i.length===0)return f(!0);let s=i.map(o=>{let c=ae(o,t),a=hs(c)?c.canMatch(n,e):q(t,()=>c(n,e));return V(a)});return f(s).pipe(oe(),Hr(r))}var Ue=class{constructor(n){this.segmentGroup=n||null}},it=class extends Error{constructor(n){super(),this.urlTree=n}};function ee(t){return he(new Ue(t))}function Rs(t){return he(new w(4e3,!1))}function bs(t){return he($r(!1,E.GuardRejected))}var cn=class{constructor(n,e){this.urlSerializer=n,this.urlTree=e}lineralizeSegments(n,e){let r=[],i=e.root;for(;;){if(r=r.concat(i.segments),i.numberOfChildren===0)return f(r);if(i.numberOfChildren>1||!i.children[p])return Rs(n.redirectTo);i=i.children[p]}}applyRedirectCommands(n,e,r){let i=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),n,r);if(e.startsWith("/"))throw new it(i);return i}applyRedirectCreateUrlTree(n,e,r,i){let s=this.createSegmentGroup(n,e.root,r,i);return new F(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(n,e){let r={};return Object.entries(n).forEach(([i,s])=>{if(typeof s=="string"&&s.startsWith(":")){let c=s.substring(1);r[i]=e[c]}else r[i]=s}),r}createSegmentGroup(n,e,r,i){let s=this.createSegments(n,e.segments,r,i),o={};return Object.entries(e.children).forEach(([c,a])=>{o[c]=this.createSegmentGroup(n,a,r,i)}),new v(s,o)}createSegments(n,e,r,i){return e.map(s=>s.path.startsWith(":")?this.findPosParam(n,s,i):this.findOrReturn(s,r))}findPosParam(n,e,r){let i=r[e.path.substring(1)];if(!i)throw new w(4001,!1);return i}findOrReturn(n,e){let r=0;for(let i of e){if(i.path===n.path)return e.splice(r),i;r++}return n}},un={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Is(t,n,e,r,i){let s=yn(t,n,e);return s.matched?(r=Ji(n,r),ws(r,n,e,i).pipe(m(o=>o===!0?s:h({},un)))):f(s)}function yn(t,n,e){if(n.path==="**")return Ms(e);if(n.path==="")return n.pathMatch==="full"&&(t.hasChildren()||e.length>0)?h({},un):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let i=(n.matcher||Ci)(e,t,n);if(!i)return h({},un);let s={};Object.entries(i.posParams??{}).forEach(([c,a])=>{s[c]=a.path});let o=i.consumed.length>0?h(h({},s),i.consumed[i.consumed.length-1].parameters):s;return{matched:!0,consumedSegments:i.consumed,remainingSegments:e.slice(i.consumed.length),parameters:o,positionalParamSegments:i.posParams??{}}}function Ms(t){return{matched:!0,parameters:t.length>0?wr(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Sr(t,n,e,r){return e.length>0&&As(t,e,r)?{segmentGroup:new v(n,Ts(r,new v(e,t.children))),slicedSegments:[]}:e.length===0&&Ds(t,e,r)?{segmentGroup:new v(t.segments,Es(t,e,r,t.children)),slicedSegments:e}:{segmentGroup:new v(t.segments,t.children),slicedSegments:e}}function Es(t,n,e,r){let i={};for(let s of e)if(ot(t,n,s)&&!r[L(s)]){let o=new v([],{});i[L(s)]=o}return h(h({},r),i)}function Ts(t,n){let e={};e[p]=n;for(let r of t)if(r.path===""&&L(r)!==p){let i=new v([],{});e[L(r)]=i}return e}function As(t,n,e){return e.some(r=>ot(t,n,r)&&L(r)!==p)}function Ds(t,n,e){return e.some(r=>ot(t,n,r))}function ot(t,n,e){return(t.hasChildren()||n.length>0)&&e.pathMatch==="full"?!1:e.path===""}function Os(t,n,e,r){return L(t)!==r&&(r===p||!ot(n,e,t))?!1:yn(n,t,e).matched}function Ns(t,n,e){return n.length===0&&!t.children[e]}var ln=class{};function Ps(t,n,e,r,i,s,o="emptyOnly"){return new hn(t,n,e,r,i,o,s).recognize()}var Us=31,hn=class{constructor(n,e,r,i,s,o,c){this.injector=n,this.configLoader=e,this.rootComponentType=r,this.config=i,this.urlTree=s,this.paramsInheritanceStrategy=o,this.urlSerializer=c,this.applyRedirects=new cn(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(n){return new w(4002,`'${n.segmentGroup}'`)}recognize(){let n=Sr(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(m(e=>{let r=new Ne([],Object.freeze({}),Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,{},p,this.rootComponentType,null,{}),i=new M(r,e),s=new nt("",i),o=$i(r,[],this.urlTree.queryParams,this.urlTree.fragment);return o.queryParams=this.urlTree.queryParams,s.url=this.urlSerializer.serialize(o),this.inheritParamsAndData(s._root,null),{state:s,tree:o}}))}match(n){return this.processSegmentGroup(this.injector,this.config,n,p).pipe(Y(r=>{if(r instanceof it)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Ue?this.noMatchError(r):r}))}inheritParamsAndData(n,e){let r=n.value,i=pn(r,e,this.paramsInheritanceStrategy);r.params=Object.freeze(i.params),r.data=Object.freeze(i.data),n.children.forEach(s=>this.inheritParamsAndData(s,r))}processSegmentGroup(n,e,r,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,e,r):this.processSegment(n,e,r,r.segments,i,!0).pipe(m(s=>s instanceof M?[s]:[]))}processChildren(n,e,r){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return N(i).pipe(de(s=>{let o=r.children[s],c=es(e,s);return this.processSegmentGroup(n,c,o,s)}),On((s,o)=>(s.push(...o),s)),dt(null),Dn(),A(s=>{if(s===null)return ee(r);let o=Vr(s);return xs(o),f(o)}))}processSegment(n,e,r,i,s,o){return N(e).pipe(de(c=>this.processSegmentAgainstRoute(c._injector??n,e,c,r,i,s,o).pipe(Y(a=>{if(a instanceof Ue)return f(null);throw a}))),$(c=>!!c),Y(c=>{if(zr(c))return Ns(r,i,s)?f(new ln):ee(r);throw c}))}processSegmentAgainstRoute(n,e,r,i,s,o,c){return Os(r,i,s,o)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,i,r,s,o):this.allowRedirects&&c?this.expandSegmentAgainstRouteUsingRedirect(n,i,e,r,s,o):ee(i):ee(i)}expandSegmentAgainstRouteUsingRedirect(n,e,r,i,s,o){let{matched:c,consumedSegments:a,positionalParamSegments:u,remainingSegments:l}=yn(e,i,s);if(!c)return ee(e);i.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Us&&(this.allowRedirects=!1));let d=this.applyRedirects.applyRedirectCommands(a,i.redirectTo,u);return this.applyRedirects.lineralizeSegments(i,d).pipe(A(S=>this.processSegment(n,r,e,S.concat(l),o,!1)))}matchSegmentAgainstRoute(n,e,r,i,s){let o=Is(e,r,i,n,this.urlSerializer);return r.path==="**"&&(e.children={}),o.pipe(P(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,i).pipe(P(({routes:a})=>{let u=r._loadedInjector??n,{consumedSegments:l,remainingSegments:d,parameters:S}=c,ce=new Ne(l,S,Object.freeze(h({},this.urlTree.queryParams)),this.urlTree.fragment,js(r),L(r),r.component??r._loadedComponent??null,r,_s(r)),{segmentGroup:ue,slicedSegments:le}=Sr(e,l,d,a);if(le.length===0&&ue.hasChildren())return this.processChildren(u,a,ue).pipe(m(B=>B===null?null:new M(ce,B)));if(a.length===0&&le.length===0)return f(new M(ce,[]));let at=L(r)===s;return this.processSegment(u,a,ue,le,at?p:s,!0).pipe(m(B=>new M(ce,B instanceof M?[B]:[])))}))):ee(e)))}getChildConfig(n,e,r){return e.children?f({routes:e.children,injector:n}):e.loadChildren?e._loadedRoutes!==void 0?f({routes:e._loadedRoutes,injector:e._loadedInjector}):Cs(n,e,r,this.urlSerializer).pipe(A(i=>i?this.configLoader.loadChildren(n,e).pipe(b(s=>{e._loadedRoutes=s.routes,e._loadedInjector=s.injector})):bs(e))):f({routes:[],injector:n})}};function xs(t){t.sort((n,e)=>n.value.outlet===p?-1:e.value.outlet===p?1:n.value.outlet.localeCompare(e.value.outlet))}function Ls(t){let n=t.value.routeConfig;return n&&n.path===""}function Vr(t){let n=[],e=new Set;for(let r of t){if(!Ls(r)){n.push(r);continue}let i=n.find(s=>r.value.routeConfig===s.value.routeConfig);i!==void 0?(i.children.push(...r.children),e.add(i)):n.push(r)}for(let r of e){let i=Vr(r.children);n.push(new M(r.value,i))}return n.filter(r=>!e.has(r))}function js(t){return t.data||{}}function _s(t){return t.resolve||{}}function ks(t,n,e,r,i,s){return A(o=>Ps(t,n,e,r,o.extractedUrl,i,s).pipe(m(({state:c,tree:a})=>I(h({},o),{targetSnapshot:c,urlAfterRedirects:a}))))}function $s(t,n){return A(e=>{let{targetSnapshot:r,guards:{canActivateChecks:i}}=e;if(!i.length)return f(e);let s=new Set(i.map(a=>a.route)),o=new Set;for(let a of s)if(!o.has(a))for(let u of Br(a))o.add(u);let c=0;return N(o).pipe(de(a=>s.has(a)?Fs(a,r,t,n):(a.data=pn(a,a.parent,t).resolve,f(void 0))),b(()=>c++),ft(1),A(a=>c===o.size?f(e):k))})}function Br(t){let n=t.children.map(e=>Br(e)).flat();return[t,...n]}function Fs(t,n,e,r){let i=t.routeConfig,s=t._resolve;return i?.title!==void 0&&!jr(i)&&(s[xe]=i.title),zs(s,t,n,r).pipe(m(o=>(t._resolvedData=o,t.data=pn(t,t.parent,e).resolve,null)))}function zs(t,n,e,r){let i=Ft(t);if(i.length===0)return f({});let s={};return N(i).pipe(A(o=>Hs(t[o],n,e,r).pipe($(),b(c=>{s[o]=c}))),ft(1),An(s),Y(o=>zr(o)?k:he(o)))}function Hs(t,n,e,r){let i=Le(n)??r,s=ae(t,i),o=s.resolve?s.resolve(n,e):q(i,()=>s(n,e));return V(o)}function kt(t){return P(n=>{let e=t(n);return e?N(e).pipe(m(()=>n)):f(n)})}var qr=(()=>{class t{buildTitle(e){let r,i=e.root;for(;i!==void 0;)r=this.getResolvedTitleForRoute(i)??r,i=i.children.find(s=>s.outlet===p);return r}getResolvedTitleForRoute(e){return e.data[xe]}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:()=>g(Vs),providedIn:"root"})}}return t})(),Vs=(()=>{class t extends qr{constructor(e){super(),this.title=e}updateTitle(e){let r=this.buildTitle(e);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||t)(C(pr))}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Sn=new D("",{providedIn:"root",factory:()=>({})}),Cn=new D(""),Bs=(()=>{class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=g(bt)}loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return f(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let r=V(e.loadComponent()).pipe(m(Wr),b(s=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=s}),_e(()=>{this.componentLoaders.delete(e)})),i=new ut(r,()=>new _).pipe(ct());return this.componentLoaders.set(e,i),i}loadChildren(e,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return f({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let s=qs(r,this.compiler,e,this.onLoadEndListener).pipe(_e(()=>{this.childrenLoaders.delete(r)})),o=new ut(s,()=>new _).pipe(ct());return this.childrenLoaders.set(r,o),o}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function qs(t,n,e,r){return V(t.loadChildren()).pipe(m(Wr),A(i=>i instanceof Wn||Array.isArray(i)?f(i):N(n.compileModuleAsync(i))),m(i=>{r&&r(t);let s,o,c=!1;return Array.isArray(i)?(o=i,c=!0):(s=i.create(e).injector,o=s.get(Cn,[],{optional:!0,self:!0}).flat()),{routes:o.map(mn),injector:s}}))}function Ws(t){return t&&typeof t=="object"&&"default"in t}function Wr(t){return Ws(t)?t.default:t}var wn=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:()=>g(Gs),providedIn:"root"})}}return t})(),Gs=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,r){return e}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Zs=new D("");var Ks=(()=>{class t{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new _,this.transitionAbortSubject=new _,this.configLoader=g(Bs),this.environmentInjector=g($e),this.urlSerializer=g(fn),this.rootContexts=g(st),this.location=g(He),this.inputBindingEnabled=g(vn,{optional:!0})!==null,this.titleStrategy=g(qr),this.options=g(Sn,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=g(wn),this.createViewTransition=g(Zs,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>f(void 0),this.rootComponentType=null;let e=i=>this.events.next(new Kt(i)),r=i=>this.events.next(new Qt(i));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=e}complete(){this.transitions?.complete()}handleNavigationRequest(e){let r=++this.navigationId;this.transitions?.next(I(h(h({},this.transitions.value),e),{id:r}))}setupNavigations(e,r,i){return this.transitions=new T({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Ie,restoredState:null,currentSnapshot:i.snapshot,targetSnapshot:null,currentRouterState:i,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Q(s=>s.id!==0),m(s=>I(h({},s),{extractedUrl:this.urlHandlingStrategy.extract(s.rawUrl)})),P(s=>{let o=!1,c=!1;return f(s).pipe(P(a=>{if(this.navigationId>s.id)return this.cancelNavigationTransition(s,"",E.SupersededByNewNavigation),k;this.currentTransition=s,this.currentNavigation={id:a.id,initialUrl:a.rawUrl,extractedUrl:a.extractedUrl,trigger:a.source,extras:a.extras,previousNavigation:this.lastSuccessfulNavigation?I(h({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let u=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),l=a.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!u&&l!=="reload"){let d="";return this.events.next(new Z(a.id,this.urlSerializer.serialize(a.rawUrl),d,Bt.IgnoredSameUrlNavigation)),a.resolve(null),k}if(this.urlHandlingStrategy.shouldProcessUrl(a.rawUrl))return f(a).pipe(P(d=>{let S=this.transitions?.getValue();return this.events.next(new Te(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),S!==this.transitions?.getValue()?k:Promise.resolve(d)}),ks(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),b(d=>{s.targetSnapshot=d.targetSnapshot,s.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=I(h({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let S=new Je(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(S)}));if(u&&this.urlHandlingStrategy.shouldProcessUrl(a.currentRawUrl)){let{id:d,extractedUrl:S,source:ce,restoredState:ue,extras:le}=a,at=new Te(d,this.urlSerializer.serialize(S),ce,ue);this.events.next(at);let B=xr(this.rootComponentType).snapshot;return this.currentTransition=s=I(h({},a),{targetSnapshot:B,urlAfterRedirects:S,extras:I(h({},le),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=S,f(s)}else{let d="";return this.events.next(new Z(a.id,this.urlSerializer.serialize(a.extractedUrl),d,Bt.IgnoredByUrlHandlingStrategy)),a.resolve(null),k}}),b(a=>{let u=new qt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(u)}),m(a=>(this.currentTransition=s=I(h({},a),{guards:ns(a.targetSnapshot,a.currentSnapshot,this.rootContexts)}),s)),ds(this.environmentInjector,a=>this.events.next(a)),b(a=>{if(s.guardsResult=a.guardsResult,se(a.guardsResult))throw kr(this.urlSerializer,a.guardsResult);let u=new Wt(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot,!!a.guardsResult);this.events.next(u)}),Q(a=>a.guardsResult?!0:(this.cancelNavigationTransition(a,"",E.GuardRejected),!1)),kt(a=>{if(a.guards.canActivateChecks.length)return f(a).pipe(b(u=>{let l=new Gt(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}),P(u=>{let l=!1;return f(u).pipe($s(this.paramsInheritanceStrategy,this.environmentInjector),b({next:()=>l=!0,complete:()=>{l||this.cancelNavigationTransition(u,"",E.NoDataFromResolver)}}))}),b(u=>{let l=new Zt(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(l)}))}),kt(a=>{let u=l=>{let d=[];l.routeConfig?.loadComponent&&!l.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(l.routeConfig).pipe(b(S=>{l.component=S}),m(()=>{})));for(let S of l.children)d.push(...u(S));return d};return lt(u(a.targetSnapshot.root)).pipe(dt(null),X(1))}),kt(()=>this.afterPreactivation()),P(()=>{let{currentSnapshot:a,targetSnapshot:u}=s,l=this.createViewTransition?.(this.environmentInjector,a.root,u.root);return l?N(l).pipe(m(()=>s)):f(s)}),m(a=>{let u=Zi(e.routeReuseStrategy,a.targetSnapshot,a.currentRouterState);return this.currentTransition=s=I(h({},a),{targetRouterState:u}),this.currentNavigation.targetRouterState=u,s}),b(()=>{this.events.next(new De)}),ts(this.rootContexts,e.routeReuseStrategy,a=>this.events.next(a),this.inputBindingEnabled),X(1),b({next:a=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new z(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects))),this.titleStrategy?.updateTitle(a.targetRouterState.snapshot),a.resolve(!0)},complete:()=>{o=!0}}),Pn(this.transitionAbortSubject.pipe(b(a=>{throw a}))),_e(()=>{!o&&!c&&this.cancelNavigationTransition(s,"",E.SupersededByNewNavigation),this.currentTransition?.id===s.id&&(this.currentNavigation=null,this.currentTransition=null)}),Y(a=>{if(c=!0,Fr(a))this.events.next(new H(s.id,this.urlSerializer.serialize(s.extractedUrl),a.message,a.cancellationCode)),Yi(a)?this.events.next(new Oe(a.url)):s.resolve(!1);else{this.events.next(new Ae(s.id,this.urlSerializer.serialize(s.extractedUrl),a,s.targetSnapshot??void 0));try{s.resolve(e.errorHandler(a))}catch(u){this.options.resolveNavigationPromiseOnError?s.resolve(!1):s.reject(u)}}return k}))}))}cancelNavigationTransition(e,r,i){let s=new H(e.id,this.urlSerializer.serialize(e.extractedUrl),r,i);this.events.next(s),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==this.currentTransition?.extractedUrl.toString()&&!this.currentTransition?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Qs(t){return t!==Ie}var Ys=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:()=>g(Xs),providedIn:"root"})}}return t})(),dn=class{shouldDetach(n){return!1}store(n,e){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,e){return n.routeConfig===e.routeConfig}},Xs=(()=>{class t extends dn{static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=yt(t)))(i||t)}})()}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Gr=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:()=>g(Js),providedIn:"root"})}}return t})(),Js=(()=>{class t extends Gr{constructor(){super(...arguments),this.location=g(He),this.urlSerializer=g(fn),this.options=g(Sn,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=g(wn),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new F,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=xr(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(r=>{r.type==="popstate"&&e(r.url,r.state)})}handleRouterEvent(e,r){if(e instanceof Te)this.stateMemento=this.createStateMemento();else if(e instanceof Z)this.rawUrlTree=r.initialUrl;else if(e instanceof Je){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let i=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(i,r)}}else e instanceof De?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,r))):e instanceof H&&(e.code===E.GuardRejected||e.code===E.NoDataFromResolver)?this.restoreHistory(r):e instanceof Ae?this.restoreHistory(r,!0):e instanceof z&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,r){let i=this.urlSerializer.serialize(e);if(this.location.isCurrentPathEqualTo(i)||r.extras.replaceUrl){let s=this.browserPageId,o=h(h({},r.extras.state),this.generateNgRouterState(r.id,s));this.location.replaceState(i,"",o)}else{let s=h(h({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(i,"",s)}}restoreHistory(e,r=!1){if(this.canceledNavigationResolution==="computed"){let i=this.browserPageId,s=this.currentPageId-i;s!==0?this.location.historyGo(s):this.currentUrlTree===e.finalUrl&&s===0&&(this.resetState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(e),this.resetUrlToCurrentUrlTree())}resetState(e){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,r){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:r}:{navigationId:e}}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=yt(t)))(i||t)}})()}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Re=function(t){return t[t.COMPLETE=0]="COMPLETE",t[t.FAILED=1]="FAILED",t[t.REDIRECTING=2]="REDIRECTING",t}(Re||{});function eo(t,n){t.events.pipe(Q(e=>e instanceof z||e instanceof H||e instanceof Ae||e instanceof Z),m(e=>e instanceof z||e instanceof Z?Re.COMPLETE:(e instanceof H?e.code===E.Redirect||e.code===E.SupersededByNewNavigation:!1)?Re.REDIRECTING:Re.FAILED),Q(e=>e!==Re.REDIRECTING),X(1)).subscribe(()=>{n()})}function to(t){throw t}var no={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},ro={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Rn=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.isNgZoneEnabled=!1,this.console=g(Fe),this.stateManager=g(Gr),this.options=g(Sn,{optional:!0})||{},this.pendingTasks=g(Zn),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=g(Ks),this.urlSerializer=g(fn),this.location=g(He),this.urlHandlingStrategy=g(wn),this._events=new _,this.errorHandler=this.options.errorHandler||to,this.navigated=!1,this.routeReuseStrategy=g(Ys),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=g(Cn,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!g(vn,{optional:!0}),this.eventsSubscription=new bn,this.isNgZoneEnabled=g(j)instanceof j&&j.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(r=>{try{let i=this.navigationTransitions.currentTransition,s=this.navigationTransitions.currentNavigation;if(i!==null&&s!==null){if(this.stateManager.handleRouterEvent(r,s),r instanceof H&&r.code!==E.Redirect&&r.code!==E.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof z)this.navigated=!0;else if(r instanceof Oe){let o=this.urlHandlingStrategy.merge(r.url,i.currentRawUrl),c={info:i.extras.info,skipLocationChange:i.extras.skipLocationChange,replaceUrl:this.urlUpdateStrategy==="eager"||Qs(i.source)};this.scheduleNavigation(o,Ie,null,c,{resolve:i.resolve,reject:i.reject,promise:i.promise})}}so(r)&&this._events.next(r)}catch(i){this.navigationTransitions.transitionAbortSubject.next(i)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ie,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(e,"popstate",r)},0)})}navigateToSyncWithBrowser(e,r,i){let s={replaceUrl:!0},o=i?.navigationId?i:null;if(i){let a=h({},i);delete a.navigationId,delete a.\u0275routerPageId,Object.keys(a).length!==0&&(s.state=a)}let c=this.parseUrl(e);this.scheduleNavigation(c,r,o,s)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(mn),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,r={}){let{relativeTo:i,queryParams:s,fragment:o,queryParamsHandling:c,preserveFragment:a}=r,u=a?this.currentUrlTree.fragment:o,l=null;switch(c){case"merge":l=h(h({},this.currentUrlTree.queryParams),s);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=s||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let S=i?i.snapshot:this.routerState.snapshot.root;d=Or(S)}catch{(typeof e[0]!="string"||!e[0].startsWith("/"))&&(e=[]),d=this.currentUrlTree.root}return Nr(d,e,l,u??null)}navigateByUrl(e,r={skipLocationChange:!1}){let i=se(e)?e:this.parseUrl(e),s=this.urlHandlingStrategy.merge(i,this.rawUrlTree);return this.scheduleNavigation(s,Ie,null,r)}navigate(e,r={skipLocationChange:!1}){return io(e),this.navigateByUrl(this.createUrlTree(e,r),r)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,r){let i;if(r===!0?i=h({},no):r===!1?i=h({},ro):i=r,se(e))return gr(this.currentUrlTree,e,i);let s=this.parseUrl(e);return gr(this.currentUrlTree,s,i)}removeEmptyProps(e){return Object.entries(e).reduce((r,[i,s])=>(s!=null&&(r[i]=s),r),{})}scheduleNavigation(e,r,i,s,o){if(this.disposed)return Promise.resolve(!1);let c,a,u;o?(c=o.resolve,a=o.reject,u=o.promise):u=new Promise((d,S)=>{c=d,a=S});let l=this.pendingTasks.add();return eo(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:i,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:s,resolve:c,reject:a,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=y({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function io(t){for(let n=0;n<t.length;n++)if(t[n]==null)throw new w(4008,!1)}function so(t){return!(t instanceof De)&&!(t instanceof Oe)}var Ia=(()=>{class t{constructor(e,r,i,s,o,c){this.router=e,this.route=r,this.tabIndexAttribute=i,this.renderer=s,this.el=o,this.locationStrategy=c,this.href=null,this.commands=null,this.onChanges=new _,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;let a=o.nativeElement.tagName?.toLowerCase();this.isAnchorElement=a==="a"||a==="area",this.isAnchorElement?this.subscription=e.events.subscribe(u=>{u instanceof z&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(e){e!=null?(this.commands=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(e,r,i,s,o){let c=this.urlTree;if(c===null||this.isAnchorElement&&(e!==0||r||i||s||o||typeof this.target=="string"&&this.target!="_self"))return!0;let a={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(c,a),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let r=this.href===null?null:zn(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(e,r){let i=this.renderer,s=this.el.nativeElement;r!==null?i.setAttribute(s,e,r):i.removeAttribute(s,e)}get urlTree(){return this.commands===null?null:this.router.createUrlTree(this.commands,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||t)(J(Rn),J(K),jn("tabindex"),J(Vn),J(kn),J(Et))}}static{this.\u0275dir=gt({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,i){r&1&&Yn("click",function(o){return i.onClick(o.button,o.ctrlKey,o.shiftKey,o.altKey,o.metaKey)}),r&2&&Kn("target",i.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[ke.HasDecoratorInputTransform,"preserveFragment","preserveFragment",ze],skipLocationChange:[ke.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",ze],replaceUrl:[ke.HasDecoratorInputTransform,"replaceUrl","replaceUrl",ze],routerLink:"routerLink"},standalone:!0,features:[qn,mt]})}}return t})();var oo=new D("");function Ma(t,...n){return vt([{provide:Cn,multi:!0,useValue:t},[],{provide:K,useFactory:ao,deps:[Rn]},{provide:er,multi:!0,useFactory:co},n.map(e=>e.\u0275providers)])}function ao(t){return t.routerState.root}function co(){let t=g(_n);return n=>{let e=t.get(Rt);if(n!==e.components[0])return;let r=t.get(Rn),i=t.get(uo);t.get(lo)===1&&r.initialNavigation(),t.get(ho,null,pt.Optional)?.setUpPreloading(),t.get(oo,null,pt.Optional)?.init(),r.resetRootComponentType(e.componentTypes[0]),i.closed||(i.next(),i.complete(),i.unsubscribe())}}var uo=new D("",{factory:()=>new _}),lo=new D("",{providedIn:"root",factory:()=>1});var ho=new D("");export{Bo as a,K as b,Gi as c,Rn as d,Ia as e,Ma as f};
