import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { WorkerControllerService, WorkerUpdateDto, WorkerResponseDto } from '../../generated-sources/openapi';

@Component({
  selector: 'app-worker-edit',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './worker-edit.component.html',
  styleUrl: './worker-edit.component.css'
})
export class WorkerEditComponent implements OnInit {
  private fb = inject(FormBuilder);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private workerService = inject(WorkerControllerService);

  workerForm: FormGroup;
  workerId: string | null = null;
  isLoading = false;
  isSubmitting = false;
  submitError: string | null = null;
  submitSuccess = false;
  loadError: string | null = null;

  // Common job types for the dropdown
  jobTypes = [
    'Plumber',
    'Electrician',
    'Carpenter',
    'Painter',
    'Cleaner',
    'Gardener',
    'Mechanic',
    'Cook',
    'Driver',
    'Handyman',
    'Other'
  ];

  constructor() {
    this.workerForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      jobType: ['', Validators.required],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\+?[\d\s\-\(\)]+$/)]],
      location: ['', [Validators.required, Validators.minLength(2)]],
      description: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  ngOnInit(): void {
    this.workerId = this.route.snapshot.paramMap.get('id');
    if (this.workerId) {
      this.loadWorkerData();
    } else {
      this.loadError = 'Worker ID not found';
    }
  }

  loadWorkerData(): void {
    if (!this.workerId) return;

    this.isLoading = true;
    this.loadError = null;

    this.workerService.getWorkerById(this.workerId).subscribe({
      next: (worker) => {
        this.populateForm(worker);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading worker data:', error);
        this.loadError = 'Failed to load worker data. Please try again.';
        this.isLoading = false;
      }
    });
  }

  populateForm(worker: WorkerResponseDto): void {
    this.workerForm.patchValue({
      fullName: worker.fullName || '',
      jobType: worker.jobType || '',
      phoneNumber: worker.phoneNumber || '',
      location: worker.location || '',
      description: worker.description || ''
    });
  }

  onSubmit(): void {
    if (this.workerForm.valid && !this.isSubmitting && this.workerId) {
      this.isSubmitting = true;
      this.submitError = null;
      this.submitSuccess = false;

      const workerData: WorkerUpdateDto = {
        fullName: this.workerForm.value.fullName,
        jobType: this.workerForm.value.jobType,
        phoneNumber: this.workerForm.value.phoneNumber,
        location: this.workerForm.value.location,
        description: this.workerForm.value.description
      };

      this.workerService.updateWorker(this.workerId, workerData).subscribe({
        next: (response) => {
          console.log('Worker updated successfully:', response);
          this.submitSuccess = true;
          this.isSubmitting = false;

          // Redirect to worker detail page after successful update
          setTimeout(() => {
            this.router.navigate(['/workers', this.workerId]);
          }, 2000);
        },
        error: (error) => {
          console.error('Error updating worker:', error);
          this.submitError = 'Failed to update worker profile. Please try again.';
          this.isSubmitting = false;
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.workerForm.controls).forEach(key => {
        this.workerForm.get(key)?.markAsTouched();
      });
    }
  }

  goBack(): void {
    if (this.workerId) {
      this.router.navigate(['/workers', this.workerId]);
    } else {
      this.router.navigate(['/workers']);
    }
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.workerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.workerForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['minlength']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors['pattern']) {
        return 'Please enter a valid phone number';
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      fullName: 'Full Name',
      jobType: 'Job Type',
      phoneNumber: 'Phone Number',
      location: 'Location',
      description: 'Description'
    };
    return displayNames[fieldName] || fieldName;
  }
}
