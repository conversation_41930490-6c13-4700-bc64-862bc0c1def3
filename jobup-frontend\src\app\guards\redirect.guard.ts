import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const redirectGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('RedirectGuard - Checking route:', state.url);
  const hasToken = authService.hasToken();
  console.log('RedirectGuard - User has token:', hasToken);

  // If user is already authenticated, redirect to home
  if (hasToken) {
    console.log('RedirectGuard - User authenticated, redirecting to /home');
    router.navigate(['/home']);
    return false;
  }

  console.log('RedirectGuard - User not authenticated, allowing access to auth page');
  // If not authenticated, allow access to auth pages
  return true;
};
