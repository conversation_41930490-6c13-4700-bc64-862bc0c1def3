import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const redirectGuard: CanActivateFn = (route, _state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // If user is already authenticated, redirect to home
  if (authService.hasToken()) {
    router.navigate(['/home']);
    return false;
  }

  // If not authenticated, allow access to auth pages
  return true;
};
