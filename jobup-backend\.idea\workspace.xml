<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f52db746-bc94-4e12-9159-06919e10c24f" name="Changes" comment="Initial backend commit">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/jobup/controller/AuthController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/jobup/controller/AuthController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/jobup/dto/WorkerCreateDto.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/jobup/dto/WorkerCreateDto.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/jobup/entities/User.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/jobup/entities/User.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/jobup/mapper/WorkerMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/jobup/mapper/WorkerMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/jobup/services/WorkerService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/jobup/services/WorkerService.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Dockerfile" />
        <option value="Enum" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {}
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/firas-mah/jobup-backend.git&quot;,
    &quot;accountId&quot;: &quot;e62dd1e0-e620-45c4-bc12-add7e575d8d3&quot;
  }
}</component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/java/com/example/jobup/services/WorkerService.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zXQcCUcWizNdN5N6A0BwUo2z5p" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.JobUp [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.JobUp [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.JobUp [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JobUpApplication.executor&quot;: &quot;Run&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;firas-branch&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="JobUpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="JobUp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.jobup.JobUpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.21565.193" />
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-IU-243.21565.193" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f52db746-bc94-4e12-9159-06919e10c24f" name="Changes" comment="" />
      <created>1751875020960</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751875020960</updated>
      <workItem from="1751875022097" duration="2455000" />
      <workItem from="1751877601127" duration="6759000" />
      <workItem from="1751886000911" duration="53000" />
      <workItem from="1751886068431" duration="2734000" />
      <workItem from="1751889050329" duration="1076000" />
      <workItem from="1751956897364" duration="662000" />
      <workItem from="1751957614027" duration="9000" />
      <workItem from="1751957650483" duration="761000" />
      <workItem from="1751958958632" duration="2204000" />
      <workItem from="1751975495599" duration="46000" />
      <workItem from="1752049266950" duration="9837000" />
      <workItem from="1752066496490" duration="102000" />
      <workItem from="1752066609118" duration="315000" />
      <workItem from="1752130580679" duration="9287000" />
      <workItem from="1752153246075" duration="1973000" />
      <workItem from="1752479186875" duration="9623000" />
      <workItem from="1752576545588" duration="11000" />
      <workItem from="1752576825492" duration="2502000" />
      <workItem from="1752580818268" duration="1543000" />
      <workItem from="1752652580992" duration="52000" />
      <workItem from="1752652649528" duration="1634000" />
      <workItem from="1752654751947" duration="254000" />
      <workItem from="1752655027961" duration="4348000" />
      <workItem from="1752915860991" duration="4996000" />
      <workItem from="1752998475563" duration="10066000" />
      <workItem from="1753016757970" duration="212000" />
      <workItem from="1753082810525" duration="9472000" />
      <workItem from="1753096884953" duration="1030000" />
      <workItem from="1753167126317" duration="11172000" />
    </task>
    <task id="LOCAL-00001" summary="Initial backend commit">
      <option name="closed" value="true" />
      <created>1752243821764</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752243821764</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Initial backend commit" />
    <option name="LAST_COMMIT_MESSAGE" value="Initial backend commit" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>