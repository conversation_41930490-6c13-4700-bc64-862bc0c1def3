"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouteParamtypes = void 0;
var RouteParamtypes;
(function (RouteParamtypes) {
    RouteParamtypes[RouteParamtypes["REQUEST"] = 0] = "REQUEST";
    RouteParamtypes[RouteParamtypes["RESPONSE"] = 1] = "RESPONSE";
    RouteParamtypes[RouteParamtypes["NEXT"] = 2] = "NEXT";
    RouteParamtypes[RouteParamtypes["BODY"] = 3] = "BODY";
    RouteParamtypes[RouteParamtypes["QUERY"] = 4] = "QUERY";
    RouteParamtypes[RouteParamtypes["PARAM"] = 5] = "PARAM";
    RouteParamtypes[RouteParamtypes["HEADERS"] = 6] = "HEADERS";
    RouteParamtypes[RouteParamtypes["SESSION"] = 7] = "SESSION";
    RouteParamtypes[RouteParamtypes["FILE"] = 8] = "FILE";
    RouteParamtypes[RouteParamtypes["FILES"] = 9] = "FILES";
    RouteParamtypes[RouteParamtypes["HOST"] = 10] = "HOST";
    RouteParamtypes[RouteParamtypes["IP"] = 11] = "IP";
    RouteParamtypes[RouteParamtypes["RAW_BODY"] = 12] = "RAW_BODY";
})(RouteParamtypes || (exports.RouteParamtypes = RouteParamtypes = {}));
