import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { WorkerControllerService, WorkerCreateDto } from '../../generated-sources/openapi';

@Component({
  selector: 'app-create-worker',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './create-worker.component.html',
  styleUrl: './create-worker.component.css'
})
export class CreateWorkerComponent {
  private fb = inject(FormBuilder);
  private workerService = inject(WorkerControllerService);

  workerForm: FormGroup;
  isSubmitting = false;
  submitError: string | null = null;
  submitSuccess = false;

  // Common job types for the dropdown
  jobTypes = [
    'Plumber',
    'Electrician',
    'Carpenter',
    'Painter',
    'Cleaner',
    'Gardener',
    'Mechanic',
    'Cook',
    'Driver',
    '<PERSON>yman',
    'Other'
  ];

  constructor() {
    this.workerForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      jobType: ['', Validators.required],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\+?[\d\s\-\(\)]+$/)]],
      location: ['', [Validators.required, Validators.minLength(2)]],
      description: ['', [Validators.required, Validators.minLength(10)]]
    });
  }

  onSubmit(): void {
    if (this.workerForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.submitError = null;
      this.submitSuccess = false;

      const workerData: WorkerCreateDto = {
        jobType: this.workerForm.value.jobType,
        phoneNumber: this.workerForm.value.phoneNumber,
        location: this.workerForm.value.location,
        description: this.workerForm.value.description
      };

      this.workerService.createWorker(workerData).subscribe({
        next: (response) => {
          console.log('Worker created successfully:', response);
          this.submitSuccess = true;
          this.isSubmitting = false;

          // Reset form after successful submission
          setTimeout(() => {
            this.workerForm.reset();
            this.submitSuccess = false;
          }, 3000);
        },
        error: (error) => {
          console.error('Error creating worker:', error);
          this.submitError = 'Failed to create worker profile. Please try again.';
          this.isSubmitting = false;
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.workerForm.controls).forEach(key => {
        this.workerForm.get(key)?.markAsTouched();
      });
    }
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.workerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.workerForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['minlength']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors['pattern']) {
        return 'Please enter a valid phone number';
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      fullName: 'Full Name',
      jobType: 'Job Type',
      phoneNumber: 'Phone Number',
      location: 'Location',
      description: 'Description'
    };
    return displayNames[fieldName] || fieldName;
  }
}
