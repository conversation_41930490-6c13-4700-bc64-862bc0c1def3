import{a as k,b as d,c as S,d as O,e as M,f as w,g as F,h as E,i as N,j as I,l as T,m as j}from"./chunk-XXRJEYVX.js";import{Ca as c,Ea as m,Fa as u,Ga as n,Ha as r,Ia as g,Ka as C,La as f,Ma as i,Oa as p,R as b,Ra as v,U as h,ib as x,jb as _,mb as y,ra as o,ub as P}from"./chunk-T3NICLM4.js";function W(e,s){e&1&&(n(0,"div",24)(1,"i",25),i(2,"\u2713"),r(),i(3," Worker profile created successfully! You can create another profile or the form will reset automatically. "),r())}function D(e,s){if(e&1&&(n(0,"div",26)(1,"i",27),i(2,"\u26A0"),r(),i(3),r()),e&2){let t=f();o(3),p(" ",t.submitError," ")}}function z(e,s){if(e&1&&(n(0,"div",28),i(1),r()),e&2){let t=f();o(),p(" ",t.getFieldError("fullName")," ")}}function q(e,s){if(e&1&&(n(0,"option",29),i(1),r()),e&2){let t=s.$implicit;m("value",t),o(),p(" ",t," ")}}function L(e,s){if(e&1&&(n(0,"div",28),i(1),r()),e&2){let t=f();o(),p(" ",t.getFieldError("jobType")," ")}}function G(e,s){if(e&1&&(n(0,"div",28),i(1),r()),e&2){let t=f();o(),p(" ",t.getFieldError("phoneNumber")," ")}}function $(e,s){if(e&1&&(n(0,"div",28),i(1),r()),e&2){let t=f();o(),p(" ",t.getFieldError("location")," ")}}function A(e,s){if(e&1&&(n(0,"div",28),i(1),r()),e&2){let t=f();o(),p(" ",t.getFieldError("description")," ")}}function V(e,s){e&1&&(n(0,"span"),i(1,"Create Profile"),r())}function J(e,s){e&1&&(n(0,"span"),g(1,"i",30),i(2," Creating Profile... "),r())}var ee=(()=>{class e{constructor(){this.fb=b(T),this.workerService=b(P),this.isSubmitting=!1,this.submitError=null,this.submitSuccess=!1,this.jobTypes=["Plumber","Electrician","Carpenter","Painter","Cleaner","Gardener","Mechanic","Cook","Driver","Handyman","Other"],this.workerForm=this.fb.group({fullName:["",[d.required,d.minLength(2)]],jobType:["",d.required],phoneNumber:["",[d.required,d.pattern(/^\+?[\d\s\-\(\)]+$/)]],location:["",[d.required,d.minLength(2)]],description:["",[d.required,d.minLength(10)]]})}onSubmit(){if(this.workerForm.valid&&!this.isSubmitting){this.isSubmitting=!0,this.submitError=null,this.submitSuccess=!1;let t={fullName:this.workerForm.value.fullName,jobType:this.workerForm.value.jobType,phoneNumber:this.workerForm.value.phoneNumber,location:this.workerForm.value.location,description:this.workerForm.value.description};this.workerService.createWorker(t).subscribe({next:a=>{console.log("Worker created successfully:",a),this.submitSuccess=!0,this.isSubmitting=!1,setTimeout(()=>{this.workerForm.reset(),this.submitSuccess=!1},3e3)},error:a=>{console.error("Error creating worker:",a),this.submitError="Failed to create worker profile. Please try again.",this.isSubmitting=!1}})}else Object.keys(this.workerForm.controls).forEach(t=>{this.workerForm.get(t)?.markAsTouched()})}isFieldInvalid(t){let a=this.workerForm.get(t);return!!(a&&a.invalid&&(a.dirty||a.touched))}getFieldError(t){let a=this.workerForm.get(t);if(a&&a.errors&&(a.dirty||a.touched)){if(a.errors.required)return`${this.getFieldDisplayName(t)} is required`;if(a.errors.minlength)return`${this.getFieldDisplayName(t)} must be at least ${a.errors.minlength.requiredLength} characters`;if(a.errors.pattern)return"Please enter a valid phone number"}return""}getFieldDisplayName(t){return{fullName:"Full Name",jobType:"Job Type",phoneNumber:"Phone Number",location:"Location",description:"Description"}[t]||t}static{this.\u0275fac=function(a){return new(a||e)}}static{this.\u0275cmp=h({type:e,selectors:[["app-create-worker"]],standalone:!0,features:[v],decls:52,vars:24,consts:[[1,"create-worker-container"],[1,"form-wrapper"],[1,"header"],["class","alert alert-success",4,"ngIf"],["class","alert alert-error",4,"ngIf"],[1,"worker-form",3,"ngSubmit","formGroup"],[1,"form-group"],["for","fullName",1,"form-label"],[1,"required"],["type","text","id","fullName","formControlName","fullName","placeholder","Enter your full name",1,"form-input"],["class","error-message",4,"ngIf"],["for","jobType",1,"form-label"],["id","jobType","formControlName","jobType",1,"form-select"],["value",""],[3,"value",4,"ngFor","ngForOf"],["for","phoneNumber",1,"form-label"],["type","tel","id","phoneNumber","formControlName","phoneNumber","placeholder","Enter your phone number",1,"form-input"],["for","location",1,"form-label"],["type","text","id","location","formControlName","location","placeholder","Enter your city or area",1,"form-input"],["for","description",1,"form-label"],["id","description","formControlName","description","placeholder","Describe your skills, experience, and services you offer...","rows","4",1,"form-textarea"],[1,"form-actions"],["type","submit",1,"submit-btn",3,"disabled"],[4,"ngIf"],[1,"alert","alert-success"],[1,"success-icon"],[1,"alert","alert-error"],[1,"error-icon"],[1,"error-message"],[3,"value"],[1,"loading-spinner"]],template:function(a,l){a&1&&(n(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1"),i(4,"Create Worker Profile"),r(),n(5,"p"),i(6,"Fill out the form below to create your worker profile and start connecting with potential clients."),r()(),c(7,W,4,0,"div",3)(8,D,4,1,"div",4),n(9,"form",5),C("ngSubmit",function(){return l.onSubmit()}),n(10,"div",6)(11,"label",7),i(12," Full Name "),n(13,"span",8),i(14,"*"),r()(),g(15,"input",9),c(16,z,2,1,"div",10),r(),n(17,"div",6)(18,"label",11),i(19," Job Type "),n(20,"span",8),i(21,"*"),r()(),n(22,"select",12)(23,"option",13),i(24,"Select your job type"),r(),c(25,q,2,2,"option",14),r(),c(26,L,2,1,"div",10),r(),n(27,"div",6)(28,"label",15),i(29," Phone Number "),n(30,"span",8),i(31,"*"),r()(),g(32,"input",16),c(33,G,2,1,"div",10),r(),n(34,"div",6)(35,"label",17),i(36," Location "),n(37,"span",8),i(38,"*"),r()(),g(39,"input",18),c(40,$,2,1,"div",10),r(),n(41,"div",6)(42,"label",19),i(43," Description "),n(44,"span",8),i(45,"*"),r()(),g(46,"textarea",20),c(47,A,2,1,"div",10),r(),n(48,"div",21)(49,"button",22),c(50,V,2,0,"span",23)(51,J,3,0,"span",23),r()()()()()),a&2&&(o(7),m("ngIf",l.submitSuccess),o(),m("ngIf",l.submitError),o(),m("formGroup",l.workerForm),o(6),u("error",l.isFieldInvalid("fullName")),o(),m("ngIf",l.isFieldInvalid("fullName")),o(6),u("error",l.isFieldInvalid("jobType")),o(3),m("ngForOf",l.jobTypes),o(),m("ngIf",l.isFieldInvalid("jobType")),o(6),u("error",l.isFieldInvalid("phoneNumber")),o(),m("ngIf",l.isFieldInvalid("phoneNumber")),o(6),u("error",l.isFieldInvalid("location")),o(),m("ngIf",l.isFieldInvalid("location")),o(6),u("error",l.isFieldInvalid("description")),o(),m("ngIf",l.isFieldInvalid("description")),o(2),u("loading",l.isSubmitting),m("disabled",l.isSubmitting),o(),m("ngIf",!l.isSubmitting),o(),m("ngIf",l.isSubmitting))},dependencies:[y,x,_,j,M,N,I,k,E,S,O,w,F],styles:[".create-worker-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:2rem 1rem;display:flex;align-items:center;justify-content:center}.form-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 20px 40px #0000001a;padding:2.5rem;width:100%;max-width:600px}.header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2d3748;font-size:2rem;font-weight:700;margin-bottom:.5rem}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;font-size:1rem;line-height:1.5}.alert[_ngcontent-%COMP%]{padding:1rem;border-radius:8px;margin-bottom:1.5rem;display:flex;align-items:center;gap:.5rem}.alert-success[_ngcontent-%COMP%]{background-color:#f0fff4;border:1px solid #9ae6b4;color:#276749}.alert-error[_ngcontent-%COMP%]{background-color:#fed7d7;border:1px solid #feb2b2;color:#c53030}.success-icon[_ngcontent-%COMP%], .error-icon[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem}.worker-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column}.form-label[_ngcontent-%COMP%]{font-weight:600;color:#2d3748;margin-bottom:.5rem;font-size:.95rem}.required[_ngcontent-%COMP%]{color:#e53e3e}.form-input[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%], .form-textarea[_ngcontent-%COMP%]{padding:.75rem;border:2px solid #e2e8f0;border-radius:8px;font-size:1rem;transition:all .2s ease;background-color:#fff}.form-input[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus, .form-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.form-input.error[_ngcontent-%COMP%], .form-select.error[_ngcontent-%COMP%], .form-textarea.error[_ngcontent-%COMP%]{border-color:#e53e3e;box-shadow:0 0 0 3px #e53e3e1a}.form-textarea[_ngcontent-%COMP%]{resize:vertical;min-height:100px}.error-message[_ngcontent-%COMP%]{color:#e53e3e;font-size:.875rem;margin-top:.25rem}.form-actions[_ngcontent-%COMP%]{margin-top:1rem}.submit-btn[_ngcontent-%COMP%]{width:100%;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;gap:.5rem}.submit-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.submit-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed;transform:none}.submit-btn.loading[_ngcontent-%COMP%]{background:#a0aec0}.loading-spinner[_ngcontent-%COMP%]{width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.create-worker-container[_ngcontent-%COMP%]{padding:1rem}.form-wrapper[_ngcontent-%COMP%]{padding:1.5rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem}}@media (max-width: 480px){.form-wrapper[_ngcontent-%COMP%]{padding:1rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.25rem}}"]})}}return e})();export{ee as CreateWorkerComponent};
