import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  console.log('AuthGuard - Checking route:', state.url);
  const hasToken = authService.hasToken();
  console.log('AuthGuard - User has token:', hasToken);

  if (hasToken) {
    console.log('AuthGuard - Access granted');
    return true;
  } else {
    console.log('AuthGuard - Access denied, redirecting to /sign-in');
    router.navigate(['/sign-in']);
    return false;
  }
};
