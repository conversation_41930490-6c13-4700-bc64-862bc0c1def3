import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavigationDashboardService {
  private currentDashboard$ = new BehaviorSubject<'client' | 'worker'>('client');

  switchToWorkerDashboard(): void {
    this.currentDashboard$.next('worker');
  }

  switchToClientDashboard(): void {
    this.currentDashboard$.next('client');
  }

  getCurrentDashboard(): Observable<'client' | 'worker'> {
    return this.currentDashboard$.asObservable();
  }
}