<div class="worker-detail-container">
  <!-- Navigation -->
  <div class="navigation">
    <button class="back-btn" (click)="goBack()">
      <i class="back-icon">←</i>
      Back to Workers
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading worker details...</p>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="error-message">
    <i class="error-icon">⚠</i>
    {{ error }}
    <button class="retry-btn" (click)="loadWorkerDetail()">Retry</button>
  </div>

  <!-- Worker Details -->
  <div *ngIf="!isLoading && !error && worker" class="worker-detail-card">
    <!-- Header Section -->
    <div class="worker-header">
      <div class="worker-avatar">
        {{ worker.fullName?.charAt(0)?.toUpperCase() }}
      </div>
      <div class="worker-info">
        <h1 class="worker-name">{{ worker.fullName }}</h1>
        <p class="worker-job">{{ worker.jobType }}</p>
        <div class="rating">
          <span class="stars">
            <span *ngFor="let star of getStarRating(worker.rating)" class="star">{{ star }}</span>
          </span>
          <span class="rating-text">({{ worker.rating || 0 }}/5 rating)</span>
        </div>
      </div>
      <div class="actions">
        <button class="edit-btn" (click)="editWorker()">
          <i class="edit-icon">✏️</i>
          Edit Profile
        </button>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="info-section">
      <h2>Contact Information</h2>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">
            <i class="icon">📞</i>
            Phone Number
          </div>
          <div class="info-value">
            {{ worker.phoneNumber }}
            <button class="action-btn" (click)="callWorker()" title="Call">
              📞
            </button>
          </div>
        </div>

        <div class="info-item">
          <div class="info-label">
            <i class="icon">📍</i>
            Location
          </div>
          <div class="info-value">
            {{ worker.location }}
            <button class="action-btn" (click)="getDirections()" title="Get Directions">
              🗺️
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Professional Information -->
    <div class="info-section">
      <h2>Professional Information</h2>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">
            <i class="icon">💼</i>
            Job Type
          </div>
          <div class="info-value">{{ worker.jobType }}</div>
        </div>

        <div class="info-item">
          <div class="info-label">
            <i class="icon">⭐</i>
            Rating
          </div>
          <div class="info-value">
            <span class="stars">
              <span *ngFor="let star of getStarRating(worker.rating)" class="star">{{ star }}</span>
            </span>
            <span class="rating-text">{{ worker.rating || 0 }}/5</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="info-section">
      <h2>About</h2>
      <div class="description">
        <p>{{ worker.description || 'No description available.' }}</p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
      <button class="primary-btn" (click)="callWorker()">
        <i class="icon">📞</i>
        Contact Worker
      </button>
      <button class="secondary-btn" (click)="getDirections()">
        <i class="icon">🗺️</i>
        Get Directions
      </button>
      <button class="secondary-btn" (click)="editWorker()">
        <i class="icon">✏️</i>
        Edit Profile
      </button>
    </div>
  </div>
</div>
