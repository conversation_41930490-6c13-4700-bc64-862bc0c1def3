import{a as _}from"./chunk-6FPAJVTP.js";import{e as H}from"./chunk-H277OY4J.js";import{Ca as u,Ea as g,Ga as e,Ha as t,Ia as n,Ja as f,Ka as h,La as S,Ma as i,Oa as w,Ra as k,Sa as y,Ta as E,U as c,aa as p,ba as x,ca as l,da as a,jb as M,kb as b,mb as C,ra as s,sa as v}from"./chunk-T3NICLM4.js";function L(o,j){if(o&1){let r=f();e(0,"div",6)(1,"span",41),i(2),t(),e(3,"button",42),h("click",function(){p(r);let m=S();return x(m.logout())}),i(4," Logout "),t()()}if(o&2){let r=j.ngIf;s(2),w(" Welcome! (",r.roles==null?null:r.roles.join(", "),") ")}}var G=(()=>{class o{constructor(r){this.authService=r,this.currentUser$=this.authService.currentUser$}ngOnInit(){}logout(){this.authService.logout()}static{this.\u0275fac=function(d){return new(d||o)(v(_))}}static{this.\u0275cmp=c({type:o,selectors:[["app-home"]],standalone:!0,features:[k],decls:94,vars:3,consts:[[1,"min-h-screen","bg-gray-50"],[1,"bg-white","shadow"],[1,"max-w-7xl","mx-auto","px-4","sm:px-6","lg:px-8"],[1,"flex","justify-between","h-16"],[1,"flex","items-center"],[1,"text-xl","font-bold","text-gray-900"],[1,"flex","items-center","space-x-4"],["class","flex items-center space-x-4",4,"ngIf"],[1,"max-w-7xl","mx-auto","py-6","sm:px-6","lg:px-8"],[1,"px-4","py-6","sm:px-0"],[1,"text-center"],[1,"text-3xl","font-extrabold","text-gray-900","sm:text-4xl"],[1,"mt-4","text-lg","text-gray-600"],[1,"mt-10"],[1,"grid","grid-cols-1","gap-4","sm:grid-cols-2","lg:grid-cols-3"],[1,"relative","rounded-lg","border","border-gray-300","bg-white","px-6","py-5","shadow-sm","hover:border-gray-400"],[1,"flex","items-center","space-x-3"],[1,"flex-shrink-0"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"h-8","w-8","text-indigo-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"],[1,"min-w-0","flex-1"],["routerLink","/workers",1,"focus:outline-none"],["aria-hidden","true",1,"absolute","inset-0"],[1,"text-sm","font-medium","text-gray-900"],[1,"text-sm","text-gray-500"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"h-8","w-8","text-green-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M12 6v6m0 0v6m0-6h6m-6 0H6"],["routerLink","/workers/create",1,"focus:outline-none"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"h-8","w-8","text-blue-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"],[1,"focus:outline-none"],[1,"text-lg","font-medium","text-gray-900","mb-4"],[1,"grid","grid-cols-1","gap-4","sm:grid-cols-3"],[1,"bg-white","overflow-hidden","shadow","rounded-lg"],[1,"p-5"],["fill","none","stroke","currentColor","viewBox","0 0 24 24",1,"h-6","w-6","text-gray-400"],[1,"ml-5","w-0","flex-1"],[1,"text-sm","font-medium","text-gray-500","truncate"],[1,"text-lg","font-medium","text-gray-900"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"],[1,"text-sm","text-gray-700"],[1,"bg-red-600","hover:bg-red-700","text-white","px-4","py-2","rounded-md","text-sm","font-medium",3,"click"]],template:function(d,m){d&1&&(e(0,"div",0)(1,"nav",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"h1",5),i(6,"JobUp"),t()(),e(7,"div",6),u(8,L,5,1,"div",7),y(9,"async"),t()()()(),e(10,"main",8)(11,"div",9)(12,"div",10)(13,"h2",11),i(14," Welcome to JobUp "),t(),e(15,"p",12),i(16," Your platform for connecting with skilled workers and finding job opportunities. "),t()(),e(17,"div",13)(18,"div",14)(19,"div",15)(20,"div",16)(21,"div",17),l(),e(22,"svg",18),n(23,"path",19),t()(),a(),e(24,"div",20)(25,"a",21),n(26,"span",22),e(27,"p",23),i(28,"Browse Workers"),t(),e(29,"p",24),i(30,"Find skilled professionals for your projects"),t()()()()(),e(31,"div",15)(32,"div",16)(33,"div",17),l(),e(34,"svg",25),n(35,"path",26),t()(),a(),e(36,"div",20)(37,"a",27),n(38,"span",22),e(39,"p",23),i(40,"Add Worker"),t(),e(41,"p",24),i(42,"Register a new worker profile"),t()()()()(),e(43,"div",15)(44,"div",16)(45,"div",17),l(),e(46,"svg",28),n(47,"path",29),t()(),a(),e(48,"div",20)(49,"div",30)(50,"p",23),i(51,"My Profile"),t(),e(52,"p",24),i(53,"Manage your account settings"),t()()()()()()(),e(54,"div",13)(55,"h3",31),i(56,"Quick Stats"),t(),e(57,"div",32)(58,"div",33)(59,"div",34)(60,"div",4)(61,"div",17),l(),e(62,"svg",35),n(63,"path",19),t()(),a(),e(64,"div",36)(65,"dl")(66,"dt",37),i(67,"Total Workers"),t(),e(68,"dd",38),i(69,"-"),t()()()()()(),e(70,"div",33)(71,"div",34)(72,"div",4)(73,"div",17),l(),e(74,"svg",35),n(75,"path",39),t()(),a(),e(76,"div",36)(77,"dl")(78,"dt",37),i(79,"Active Projects"),t(),e(80,"dd",38),i(81,"-"),t()()()()()(),e(82,"div",33)(83,"div",34)(84,"div",4)(85,"div",17),l(),e(86,"svg",35),n(87,"path",40),t()(),a(),e(88,"div",36)(89,"dl")(90,"dt",37),i(91,"Average Rating"),t(),e(92,"dd",38),i(93,"-"),t()()()()()()()()()()()),d&2&&(s(8),g("ngIf",E(9,1,m.currentUser$)))},dependencies:[C,M,b,H]})}}return o})();export{G as HomeComponent};
