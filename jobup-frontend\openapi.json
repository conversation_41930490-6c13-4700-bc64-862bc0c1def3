{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8083/JobUp", "description": "Generated server url"}], "tags": [{"name": "Authentication", "description": "Authentication management APIs"}], "paths": {"/api/workers/{id}": {"get": {"tags": ["worker-controller"], "operationId": "getWorkerById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}, "put": {"tags": ["worker-controller"], "operationId": "updateWorker", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerUpdateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}, "delete": {"tags": ["worker-controller"], "operationId": "deleteWorker", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register a new user", "description": "Creates a new user with ROLE_CLIENT by default", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login user", "description": "Authenticates user and returns JWT token with roles", "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AuthResponseDto"}}}}}}}, "/api/workers": {"get": {"tags": ["worker-controller"], "operationId": "getAllWorkers", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}, "post": {"tags": ["worker-controller"], "operationId": "createWorker", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerCreateDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}, "/api/workers/search/location": {"get": {"tags": ["worker-controller"], "operationId": "searchByLocation", "parameters": [{"name": "location", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}}, "/api/workers/search/job": {"get": {"tags": ["worker-controller"], "operationId": "searchByJobType", "parameters": [{"name": "jobType", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkerResponseDto"}}}}}}}}}, "components": {"schemas": {"WorkerUpdateDto": {"type": "object", "properties": {"fullName": {"type": "string"}, "jobType": {"type": "string"}, "location": {"type": "string"}, "phoneNumber": {"type": "string"}, "description": {"type": "string"}}, "description": "DTO used to update an existing worker"}, "WorkerResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "fullName": {"type": "string"}, "jobType": {"type": "string"}, "phoneNumber": {"type": "string"}, "location": {"type": "string"}, "rating": {"type": "number", "format": "double"}, "description": {"type": "string"}}, "description": "DTO returned in responses to the frontend"}, "RegisterRequestDto": {"required": ["email", "password", "username"], "type": "object", "properties": {"username": {"maxLength": 20, "minLength": 3, "type": "string", "example": "johndoe"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"maxLength": 2147483647, "minLength": 6, "type": "string", "example": "password123"}}, "description": "DTO used to register a new user"}, "AuthResponseDto": {"type": "object", "properties": {"token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "roles": {"type": "array", "example": ["ROLE_CLIENT"], "items": {"type": "string", "example": "[\"ROLE_CLIENT\"]"}}}, "description": "DTO returned after successful authentication"}, "LoginRequestDto": {"required": ["password", "username"], "type": "object", "properties": {"username": {"type": "string", "example": "johndoe"}, "password": {"type": "string", "example": "password123"}}, "description": "DTO used for user login"}, "WorkerCreateDto": {"type": "object", "properties": {"fullName": {"type": "string", "example": "<PERSON>"}, "jobType": {"type": "string", "example": "Electrician"}, "phoneNumber": {"type": "string", "example": "+21612345678"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "description": {"type": "string", "example": "etc..."}}, "description": "DTO used to create a new worker"}}}}