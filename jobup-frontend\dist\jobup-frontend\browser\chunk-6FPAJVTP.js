import{d as u}from"./chunk-H277OY4J.js";import{I as i,L as n,Q as o,h as s,tb as a}from"./chunk-T3NICLM4.js";var g=(()=>{class r{constructor(t,e){this.authController=t,this.router=e,this.TOKEN_KEY="jobup_token",this.USER_KEY="jobup_user",this.isAuthenticatedSubject=new s(this.hasToken()),this.currentUserSubject=new s(this.getCurrentUser()),this.isAuthenticated$=this.isAuthenticatedSubject.asObservable(),this.currentUser$=this.currentUserSubject.asObservable()}register(t){return this.authController.register(t).pipe(i(e=>this.handleAuthSuccess(e)))}login(t){return this.authController.login(t).pipe(i(e=>this.handleAuthSuccess(e)))}logout(){localStorage.removeItem(this.TOKEN_KEY),localStorage.removeItem(this.USER_KEY),this.isAuthenticatedSubject.next(!1),this.currentUserSubject.next(null),this.router.navigate(["/sign-in"])}getToken(){return localStorage.getItem(this.TOKEN_KEY)}getCurrentUser(){let t=localStorage.getItem(this.USER_KEY);return t?JSON.parse(t):null}hasToken(){return!!this.getToken()}hasRole(t){return this.getCurrentUser()?.roles?.includes(t)||!1}handleAuthSuccess(t){if(t.token){localStorage.setItem(this.TOKEN_KEY,t.token);let e={roles:t.roles||[]};localStorage.setItem(this.USER_KEY,JSON.stringify(e)),this.isAuthenticatedSubject.next(!0),this.currentUserSubject.next(e),this.router.navigate(["/home"])}}static{this.\u0275fac=function(e){return new(e||r)(o(a),o(u))}}static{this.\u0275prov=n({token:r,factory:r.\u0275fac,providedIn:"root"})}}return r})();export{g as a};
