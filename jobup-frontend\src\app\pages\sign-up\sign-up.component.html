<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your JobUp account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Or
        <a routerLink="/sign-in" class="font-medium text-indigo-600 hover:text-indigo-500">
          sign in to your existing account
        </a>
      </p>
    </div>

    <form class="mt-8 space-y-6" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="rounded-md shadow-sm -space-y-px">
        <div>
          <label for="username" class="sr-only">Username</label>
          <input
            id="username"
            name="username"
            type="text"
            formControlName="username"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Username"
          />
          <div *ngIf="username?.invalid && username?.touched" class="text-red-600 text-sm mt-1">
            <div *ngIf="username?.errors?.['required']">Username is required</div>
            <div *ngIf="username?.errors?.['minlength']">Username must be at least 3 characters</div>
            <div *ngIf="username?.errors?.['maxlength']">Username must be at most 20 characters</div>
          </div>
        </div>

        <div>
          <label for="email" class="sr-only">Email</label>
          <input
            id="email"
            name="email"
            type="email"
            formControlName="email"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Email address"
          />
          <div *ngIf="email?.invalid && email?.touched" class="text-red-600 text-sm mt-1">
            <div *ngIf="email?.errors?.['required']">Email is required</div>
            <div *ngIf="email?.errors?.['email']">Please enter a valid email address</div>
          </div>
        </div>

        <div>
          <label for="password" class="sr-only">Password</label>
          <input
            id="password"
            name="password"
            type="password"
            formControlName="password"
            required
            class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Password"
          />
          <div *ngIf="password?.invalid && password?.touched" class="text-red-600 text-sm mt-1">
            <div *ngIf="password?.errors?.['required']">Password is required</div>
            <div *ngIf="password?.errors?.['minlength']">Password must be at least 6 characters</div>
          </div>
        </div>
      </div>

      <div *ngIf="errorMessage" class="text-red-600 text-sm text-center">
        {{ errorMessage }}
      </div>

      <div>
        <button
          type="submit"
          [disabled]="registerForm.invalid || isLoading"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isLoading ? 'Creating account...' : 'Create account' }}
        </button>
      </div>
    </form>
  </div>
</div>
