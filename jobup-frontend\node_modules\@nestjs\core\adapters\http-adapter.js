"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbstractHttpAdapter = void 0;
/**
 * @publicApi
 */
class AbstractHttpAdapter {
    constructor(instance) {
        this.instance = instance;
    }
    async init() { }
    use(...args) {
        return this.instance.use(...args);
    }
    get(...args) {
        return this.instance.get(...args);
    }
    post(...args) {
        return this.instance.post(...args);
    }
    head(...args) {
        return this.instance.head(...args);
    }
    delete(...args) {
        return this.instance.delete(...args);
    }
    put(...args) {
        return this.instance.put(...args);
    }
    patch(...args) {
        return this.instance.patch(...args);
    }
    propfind(...args) {
        return this.instance.propfind(...args);
    }
    proppatch(...args) {
        return this.instance.proppatch(...args);
    }
    mkcol(...args) {
        return this.instance.mkcol(...args);
    }
    copy(...args) {
        return this.instance.copy(...args);
    }
    move(...args) {
        return this.instance.move(...args);
    }
    lock(...args) {
        return this.instance.lock(...args);
    }
    unlock(...args) {
        return this.instance.unlock(...args);
    }
    all(...args) {
        return this.instance.all(...args);
    }
    search(...args) {
        return this.instance.search(...args);
    }
    options(...args) {
        return this.instance.options(...args);
    }
    listen(port, hostname, callback) {
        return this.instance.listen(port, hostname, callback);
    }
    getHttpServer() {
        return this.httpServer;
    }
    setHttpServer(httpServer) {
        this.httpServer = httpServer;
    }
    setInstance(instance) {
        this.instance = instance;
    }
    getInstance() {
        return this.instance;
    }
    normalizePath(path) {
        return path;
    }
    setOnRouteTriggered(onRouteTriggered) {
        this.onRouteTriggered = onRouteTriggered;
    }
    getOnRouteTriggered() {
        return this.onRouteTriggered;
    }
    setOnRequestHook(onRequestHook) { }
    setOnResponseHook(onResponseHook) { }
}
exports.AbstractHttpAdapter = AbstractHttpAdapter;
