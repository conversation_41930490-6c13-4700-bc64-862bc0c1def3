import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { redirectGuard } from './guards/redirect.guard';

export const routes: Routes = [
  // Authentication routes (public)
  {
    path: 'sign-in',
    loadComponent: () => import('./pages/sign-in/sign-in.component').then(m => m.SignInComponent),
    canActivate: [redirectGuard]
  },
  {
    path: 'sign-up',
    loadComponent: () => import('./pages/sign-up/sign-up.component').then(m => m.SignUpComponent),
    canActivate: [redirectGuard]
  },

  // Protected routes
  {
    path: 'home',
    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers',
    loadComponent: () => import('./pages/worker-list/worker-list.component').then(m => m.WorkerListComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/create',
    loadComponent: () => import('./pages/create-worker/create-worker.component').then(m => m.CreateWorkerComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/:id',
    loadComponent: () => import('./pages/worker-detail/worker-detail.component').then(m => m.WorkerDetailComponent),
    canActivate: [authGuard]
  },
  {
    path: 'workers/:id/edit',
    loadComponent: () => import('./pages/worker-edit/worker-edit.component').then(m => m.WorkerEditComponent),
    canActivate: [authGuard]
  },
  {
    path: 'become-worker',
    loadComponent: () => import('./pages/become-worker/become-worker.component').then(m => m.BecomeWorkerComponent),
    canActivate: [authGuard]
  },

  // Default redirects
  {
    path: '',
    redirectTo: '/sign-in',
    pathMatch: 'full'
  },
  {
    path: '**',
    redirectTo: '/sign-in'
  }
];
