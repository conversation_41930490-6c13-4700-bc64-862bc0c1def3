import{b as M,d as D}from"./chunk-H277OY4J.js";import{Ca as k,Ea as u,Ga as n,Ha as e,Ia as C,Ja as b,<PERSON> as c,<PERSON> as d,<PERSON> as t,Na as g,Oa as p,R as x,Ra as v,U as w,aa as s,ba as m,ib as h,jb as O,mb as P,ra as l,ub as E}from"./chunk-T3NICLM4.js";function y(a,f){a&1&&(n(0,"div",7),C(1,"div",8),n(2,"p"),t(3,"Loading worker details..."),e()())}function S(a,f){if(a&1){let r=b();n(0,"div",9)(1,"i",10),t(2,"\u26A0"),e(),t(3),n(4,"button",11),c("click",function(){s(r);let o=d();return m(o.loadWorkerDetail())}),t(5,"Retry"),e()()}if(a&2){let r=d();l(3),p(" ",r.error," ")}}function I(a,f){if(a&1&&(n(0,"span",37),t(1),e()),a&2){let r=f.$implicit;l(),g(r)}}function W(a,f){if(a&1&&(n(0,"span",37),t(1),e()),a&2){let r=f.$implicit;l(),g(r)}}function z(a,f){if(a&1){let r=b();n(0,"div",12)(1,"div",13)(2,"div",14),t(3),e(),n(4,"div",15)(5,"h1",16),t(6),e(),n(7,"p",17),t(8),e(),n(9,"div",18)(10,"span",19),k(11,I,2,1,"span",20),e(),n(12,"span",21),t(13),e()()(),n(14,"div",22)(15,"button",23),c("click",function(){s(r);let o=d();return m(o.editWorker())}),n(16,"i",24),t(17,"\u270F\uFE0F"),e(),t(18," Edit Profile "),e()()(),n(19,"div",25)(20,"h2"),t(21,"Contact Information"),e(),n(22,"div",26)(23,"div",27)(24,"div",28)(25,"i",29),t(26,"\u{1F4DE}"),e(),t(27," Phone Number "),e(),n(28,"div",30),t(29),n(30,"button",31),c("click",function(){s(r);let o=d();return m(o.callWorker())}),t(31," \u{1F4DE} "),e()()(),n(32,"div",27)(33,"div",28)(34,"i",29),t(35,"\u{1F4CD}"),e(),t(36," Location "),e(),n(37,"div",30),t(38),n(39,"button",32),c("click",function(){s(r);let o=d();return m(o.getDirections())}),t(40," \u{1F5FA}\uFE0F "),e()()()()(),n(41,"div",25)(42,"h2"),t(43,"Professional Information"),e(),n(44,"div",26)(45,"div",27)(46,"div",28)(47,"i",29),t(48,"\u{1F4BC}"),e(),t(49," Job Type "),e(),n(50,"div",30),t(51),e()(),n(52,"div",27)(53,"div",28)(54,"i",29),t(55,"\u2B50"),e(),t(56," Rating "),e(),n(57,"div",30)(58,"span",19),k(59,W,2,1,"span",20),e(),n(60,"span",21),t(61),e()()()()(),n(62,"div",25)(63,"h2"),t(64,"About"),e(),n(65,"div",33)(66,"p"),t(67),e()()(),n(68,"div",34)(69,"button",35),c("click",function(){s(r);let o=d();return m(o.callWorker())}),n(70,"i",29),t(71,"\u{1F4DE}"),e(),t(72," Contact Worker "),e(),n(73,"button",36),c("click",function(){s(r);let o=d();return m(o.getDirections())}),n(74,"i",29),t(75,"\u{1F5FA}\uFE0F"),e(),t(76," Get Directions "),e(),n(77,"button",36),c("click",function(){s(r);let o=d();return m(o.editWorker())}),n(78,"i",29),t(79,"\u270F\uFE0F"),e(),t(80," Edit Profile "),e()()()}if(a&2){let r,i=d();l(3),p(" ",i.worker.fullName==null||(r=i.worker.fullName.charAt(0))==null?null:r.toUpperCase()," "),l(3),g(i.worker.fullName),l(2),g(i.worker.jobType),l(3),u("ngForOf",i.getStarRating(i.worker.rating)),l(2),p("(",i.worker.rating||0,"/5 rating)"),l(16),p(" ",i.worker.phoneNumber," "),l(9),p(" ",i.worker.location," "),l(13),g(i.worker.jobType),l(8),u("ngForOf",i.getStarRating(i.worker.rating)),l(2),p("",i.worker.rating||0,"/5"),l(6),g(i.worker.description||"No description available.")}}var A=(()=>{class a{constructor(){this.route=x(M),this.router=x(D),this.workerService=x(E),this.worker=null,this.isLoading=!1,this.error=null,this.workerId=null}ngOnInit(){this.workerId=this.route.snapshot.paramMap.get("id"),this.workerId?this.loadWorkerDetail():this.error="Worker ID not found"}loadWorkerDetail(){this.workerId&&(this.isLoading=!0,this.error=null,this.workerService.getWorkerById(this.workerId).subscribe({next:r=>{this.worker=r,this.isLoading=!1},error:r=>{console.error("Error loading worker details:",r),this.error="Failed to load worker details. Please try again.",this.isLoading=!1}}))}editWorker(){this.workerId&&this.router.navigate(["/workers",this.workerId,"edit"])}goBack(){this.router.navigate(["/workers"])}getStarRating(r){let i=[],o=r||0;for(let _=1;_<=5;_++)_<=o?i.push("\u2605"):i.push("\u2606");return i}callWorker(){this.worker?.phoneNumber&&window.open(`tel:${this.worker.phoneNumber}`,"_self")}getDirections(){if(this.worker?.location){let r=encodeURIComponent(this.worker.location);window.open(`https://www.google.com/maps/search/${r}`,"_blank")}}static{this.\u0275fac=function(i){return new(i||a)}}static{this.\u0275cmp=w({type:a,selectors:[["app-worker-detail"]],standalone:!0,features:[v],decls:9,vars:3,consts:[[1,"worker-detail-container"],[1,"navigation"],[1,"back-btn",3,"click"],[1,"back-icon"],["class","loading-container",4,"ngIf"],["class","error-message",4,"ngIf"],["class","worker-detail-card",4,"ngIf"],[1,"loading-container"],[1,"loading-spinner"],[1,"error-message"],[1,"error-icon"],[1,"retry-btn",3,"click"],[1,"worker-detail-card"],[1,"worker-header"],[1,"worker-avatar"],[1,"worker-info"],[1,"worker-name"],[1,"worker-job"],[1,"rating"],[1,"stars"],["class","star",4,"ngFor","ngForOf"],[1,"rating-text"],[1,"actions"],[1,"edit-btn",3,"click"],[1,"edit-icon"],[1,"info-section"],[1,"info-grid"],[1,"info-item"],[1,"info-label"],[1,"icon"],[1,"info-value"],["title","Call",1,"action-btn",3,"click"],["title","Get Directions",1,"action-btn",3,"click"],[1,"description"],[1,"action-section"],[1,"primary-btn",3,"click"],[1,"secondary-btn",3,"click"],[1,"star"]],template:function(i,o){i&1&&(n(0,"div",0)(1,"div",1)(2,"button",2),c("click",function(){return o.goBack()}),n(3,"i",3),t(4,"\u2190"),e(),t(5," Back to Workers "),e()(),k(6,y,4,0,"div",4)(7,S,6,1,"div",5)(8,z,81,11,"div",6),e()),i&2&&(l(6),u("ngIf",o.isLoading),l(),u("ngIf",o.error),l(),u("ngIf",!o.isLoading&&!o.error&&o.worker))},dependencies:[P,h,O],styles:[".worker-detail-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#f5f7fa,#c3cfe2);padding:2rem}.navigation[_ngcontent-%COMP%]{margin-bottom:2rem}.back-btn[_ngcontent-%COMP%]{background:#fff;color:#4a5568;border:2px solid #e2e8f0;padding:.75rem 1rem;border-radius:8px;font-size:.9rem;cursor:pointer;transition:all .2s ease;display:inline-flex;align-items:center;gap:.5rem}.back-btn[_ngcontent-%COMP%]:hover{background:#f7fafc;border-color:#cbd5e0}.back-icon[_ngcontent-%COMP%]{font-size:1.2rem}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:3rem}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #e2e8f0;border-top:4px solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.error-message[_ngcontent-%COMP%]{background:#fed7d7;border:1px solid #feb2b2;color:#c53030;padding:1rem;border-radius:8px;margin-bottom:1.5rem;display:flex;align-items:center;gap:.5rem}.error-icon[_ngcontent-%COMP%]{font-size:1.2rem}.retry-btn[_ngcontent-%COMP%]{background:#c53030;color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;font-size:.9rem;cursor:pointer;margin-left:auto}.retry-btn[_ngcontent-%COMP%]:hover{background:#9c2626}.worker-detail-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 8px 16px #0000001a;overflow:hidden;max-width:800px;margin:0 auto}.worker-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:2rem;display:flex;align-items:center;gap:1.5rem}.worker-avatar[_ngcontent-%COMP%]{width:80px;height:80px;background:#fff3;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:2rem;font-weight:700;border:3px solid rgba(255,255,255,.3)}.worker-info[_ngcontent-%COMP%]{flex:1}.worker-name[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin:0 0 .5rem}.worker-job[_ngcontent-%COMP%]{font-size:1.1rem;margin:0 0 .75rem;opacity:.9}.rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.stars[_ngcontent-%COMP%]{display:flex;gap:.1rem}.star[_ngcontent-%COMP%]{color:gold;font-size:1.2rem}.rating-text[_ngcontent-%COMP%]{opacity:.8;font-size:.9rem}.actions[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.edit-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:.75rem 1rem;border-radius:8px;font-size:.9rem;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:.5rem}.edit-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.info-section[_ngcontent-%COMP%]{padding:2rem;border-bottom:1px solid #e2e8f0}.info-section[_ngcontent-%COMP%]:last-child{border-bottom:none}.info-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#2d3748;font-size:1.3rem;font-weight:600;margin:0 0 1.5rem}.info-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr;gap:1.5rem}.info-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem}.info-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;color:#4a5568;font-weight:500;font-size:.9rem}.info-value[_ngcontent-%COMP%]{color:#2d3748;font-size:1rem;display:flex;align-items:center;gap:.5rem}.action-btn[_ngcontent-%COMP%]{background:#f7fafc;border:1px solid #e2e8f0;border-radius:4px;padding:.25rem .5rem;cursor:pointer;font-size:.8rem;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{background:#edf2f7;border-color:#cbd5e0}.description[_ngcontent-%COMP%]{background:#f7fafc;border-radius:8px;padding:1.5rem;border-left:4px solid #667eea}.description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4a5568;line-height:1.6;margin:0;font-size:1rem}.action-section[_ngcontent-%COMP%]{padding:2rem;background:#f7fafc;display:flex;gap:1rem;justify-content:center;flex-wrap:wrap}.primary-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:.5rem}.primary-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.secondary-btn[_ngcontent-%COMP%]{background:#fff;color:#4a5568;border:2px solid #e2e8f0;padding:1rem 2rem;border-radius:8px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:.5rem}.secondary-btn[_ngcontent-%COMP%]:hover{background:#f7fafc;border-color:#cbd5e0;transform:translateY(-1px)}.icon[_ngcontent-%COMP%]{font-size:1rem}@media (max-width: 768px){.worker-detail-container[_ngcontent-%COMP%]{padding:1rem}.worker-header[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:1rem}.worker-name[_ngcontent-%COMP%]{font-size:1.5rem}.info-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.action-section[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.primary-btn[_ngcontent-%COMP%], .secondary-btn[_ngcontent-%COMP%]{width:100%;max-width:300px;justify-content:center}}@media (max-width: 480px){.worker-detail-container[_ngcontent-%COMP%]{padding:.5rem}.info-section[_ngcontent-%COMP%]{padding:1rem}.worker-header[_ngcontent-%COMP%]{padding:1.5rem}.worker-avatar[_ngcontent-%COMP%]{width:60px;height:60px;font-size:1.5rem}}"]})}}return a})();export{A as WorkerDetailComponent};
