import{a as I}from"./chunk-6FPAJVTP.js";import{a as C,b as u,c as E,d as F,e as L,f as M,g as T,k as q,l as N,m as k}from"./chunk-XXRJEYVX.js";import{d as _,e as b}from"./chunk-H277OY4J.js";import{Ca as d,Ea as s,Ga as i,Ha as r,Ia as c,Ka as h,La as g,Ma as a,Oa as f,Ra as y,U as v,ca as x,jb as S,mb as w,ra as o,sa as p}from"./chunk-T3NICLM4.js";function V(e,l){e&1&&(i(0,"div"),a(1,"Username is required"),r())}function j(e,l){e&1&&(i(0,"div"),a(1,"Username must be at least 3 characters"),r())}function G(e,l){if(e&1&&(i(0,"div",15),d(1,V,2,0,"div",16)(2,j,2,0,"div",16),r()),e&2){let t=g();o(),s("ngIf",t.username==null||t.username.errors==null?null:t.username.errors.required),o(),s("ngIf",t.username==null||t.username.errors==null?null:t.username.errors.minlength)}}function U(e,l){e&1&&(i(0,"div"),a(1,"Password is required"),r())}function z(e,l){e&1&&(i(0,"div"),a(1,"Password must be at least 6 characters"),r())}function D(e,l){if(e&1&&(i(0,"div",15),d(1,U,2,0,"div",16)(2,z,2,0,"div",16),r()),e&2){let t=g();o(),s("ngIf",t.password==null||t.password.errors==null?null:t.password.errors.required),o(),s("ngIf",t.password==null||t.password.errors==null?null:t.password.errors.minlength)}}function P(e,l){if(e&1&&(i(0,"div",17),a(1),r()),e&2){let t=g();o(),f(" ",t.errorMessage," ")}}function R(e,l){e&1&&(i(0,"span",18),x(),i(1,"svg",19),c(2,"circle",20)(3,"path",21),r()())}var Y=(()=>{class e{constructor(t,m,n){this.fb=t,this.authService=m,this.router=n,this.isLoading=!1,this.errorMessage="",this.loginForm=this.fb.group({username:["",[u.required,u.minLength(3)]],password:["",[u.required,u.minLength(6)]]})}onSubmit(){this.loginForm.valid&&(this.isLoading=!0,this.errorMessage="",this.authService.login(this.loginForm.value).subscribe({next:t=>{this.isLoading=!1},error:t=>{this.isLoading=!1,this.errorMessage="Invalid username or password",console.error("Login error:",t)}}))}get username(){return this.loginForm.get("username")}get password(){return this.loginForm.get("password")}static{this.\u0275fac=function(m){return new(m||e)(p(N),p(I),p(_))}}static{this.\u0275cmp=v({type:e,selectors:[["app-sign-in"]],standalone:!0,features:[y],decls:26,vars:7,consts:[[1,"min-h-screen","flex","items-center","justify-center","bg-gray-50","py-12","px-4","sm:px-6","lg:px-8"],[1,"max-w-md","w-full","space-y-8"],[1,"mt-6","text-center","text-3xl","font-extrabold","text-gray-900"],[1,"mt-2","text-center","text-sm","text-gray-600"],["routerLink","/sign-up",1,"font-medium","text-indigo-600","hover:text-indigo-500"],[1,"mt-8","space-y-6",3,"ngSubmit","formGroup"],[1,"rounded-md","shadow-sm","-space-y-px"],["for","username",1,"sr-only"],["id","username","name","username","type","text","formControlName","username","required","","placeholder","Username",1,"appearance-none","rounded-none","relative","block","w-full","px-3","py-2","border","border-gray-300","placeholder-gray-500","text-gray-900","rounded-t-md","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500","focus:z-10","sm:text-sm"],["class","text-red-600 text-sm mt-1",4,"ngIf"],["for","password",1,"sr-only"],["id","password","name","password","type","password","formControlName","password","required","","placeholder","Password",1,"appearance-none","rounded-none","relative","block","w-full","px-3","py-2","border","border-gray-300","placeholder-gray-500","text-gray-900","rounded-b-md","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500","focus:z-10","sm:text-sm"],["class","text-red-600 text-sm text-center",4,"ngIf"],["type","submit",1,"group","relative","w-full","flex","justify-center","py-2","px-4","border","border-transparent","text-sm","font-medium","rounded-md","text-white","bg-indigo-600","hover:bg-indigo-700","focus:outline-none","focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500","disabled:opacity-50","disabled:cursor-not-allowed",3,"disabled"],["class","absolute left-0 inset-y-0 flex items-center pl-3",4,"ngIf"],[1,"text-red-600","text-sm","mt-1"],[4,"ngIf"],[1,"text-red-600","text-sm","text-center"],[1,"absolute","left-0","inset-y-0","flex","items-center","pl-3"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"animate-spin","h-5","w-5","text-white"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"]],template:function(m,n){m&1&&(i(0,"div",0)(1,"div",1)(2,"div")(3,"h2",2),a(4," Sign in to JobUp "),r(),i(5,"p",3),a(6," Or "),i(7,"a",4),a(8," create a new account "),r()()(),i(9,"form",5),h("ngSubmit",function(){return n.onSubmit()}),i(10,"div",6)(11,"div")(12,"label",7),a(13,"Username"),r(),c(14,"input",8),d(15,G,3,2,"div",9),r(),i(16,"div")(17,"label",10),a(18,"Password"),r(),c(19,"input",11),d(20,D,3,2,"div",9),r()(),d(21,P,2,1,"div",12),i(22,"div")(23,"button",13),d(24,R,4,0,"span",14),a(25),r()()()()()),m&2&&(o(9),s("formGroup",n.loginForm),o(6),s("ngIf",(n.username==null?null:n.username.invalid)&&(n.username==null?null:n.username.touched)),o(5),s("ngIf",(n.password==null?null:n.password.invalid)&&(n.password==null?null:n.password.touched)),o(),s("ngIf",n.errorMessage),o(2),s("disabled",n.loginForm.invalid||n.isLoading),o(),s("ngIf",n.isLoading),o(),f(" ",n.isLoading?"Signing in...":"Sign in"," "))},dependencies:[w,S,k,L,C,E,F,q,M,T,b]})}}return e})();export{Y as SignInComponent};
