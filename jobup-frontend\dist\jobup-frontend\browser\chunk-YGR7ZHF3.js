import{a as C}from"./chunk-6FPAJVTP.js";import{a as E,b as d,c as I,d as U,e as F,f as T,g as q,k as L,l as M,m as N}from"./chunk-XXRJEYVX.js";import{d as b,e as w}from"./chunk-H277OY4J.js";import{Ca as m,Ea as l,Ga as i,Ha as n,Ia as p,Ka as _,La as c,Ma as a,Oa as f,Ra as y,U as v,ca as x,jb as h,mb as S,ra as o,sa as g}from"./chunk-T3NICLM4.js";function k(e,s){e&1&&(i(0,"div"),a(1,"Username is required"),n())}function P(e,s){e&1&&(i(0,"div"),a(1,"Username must be at least 3 characters"),n())}function R(e,s){e&1&&(i(0,"div"),a(1,"Username must be at most 20 characters"),n())}function V(e,s){if(e&1&&(i(0,"div",17),m(1,k,2,0,"div",18)(2,P,2,0,"div",18)(3,R,2,0,"div",18),n()),e&2){let t=c();o(),l("ngIf",t.username==null||t.username.errors==null?null:t.username.errors.required),o(),l("ngIf",t.username==null||t.username.errors==null?null:t.username.errors.minlength),o(),l("ngIf",t.username==null||t.username.errors==null?null:t.username.errors.maxlength)}}function j(e,s){e&1&&(i(0,"div"),a(1,"Email is required"),n())}function z(e,s){e&1&&(i(0,"div"),a(1,"Please enter a valid email address"),n())}function G(e,s){if(e&1&&(i(0,"div",17),m(1,j,2,0,"div",18)(2,z,2,0,"div",18),n()),e&2){let t=c();o(),l("ngIf",t.email==null||t.email.errors==null?null:t.email.errors.required),o(),l("ngIf",t.email==null||t.email.errors==null?null:t.email.errors.email)}}function D(e,s){e&1&&(i(0,"div"),a(1,"Password is required"),n())}function A(e,s){e&1&&(i(0,"div"),a(1,"Password must be at least 6 characters"),n())}function B(e,s){if(e&1&&(i(0,"div",17),m(1,D,2,0,"div",18)(2,A,2,0,"div",18),n()),e&2){let t=c();o(),l("ngIf",t.password==null||t.password.errors==null?null:t.password.errors.required),o(),l("ngIf",t.password==null||t.password.errors==null?null:t.password.errors.minlength)}}function H(e,s){if(e&1&&(i(0,"div",19),a(1),n()),e&2){let t=c();o(),f(" ",t.errorMessage," ")}}function J(e,s){e&1&&(i(0,"span",20),x(),i(1,"svg",21),p(2,"circle",22)(3,"path",23),n()())}var te=(()=>{class e{constructor(t,u,r){this.fb=t,this.authService=u,this.router=r,this.isLoading=!1,this.errorMessage="",this.registerForm=this.fb.group({username:["",[d.required,d.minLength(3),d.maxLength(20)]],email:["",[d.required,d.email]],password:["",[d.required,d.minLength(6)]]})}onSubmit(){this.registerForm.valid&&(this.isLoading=!0,this.errorMessage="",this.authService.register(this.registerForm.value).subscribe({next:t=>{this.isLoading=!1},error:t=>{this.isLoading=!1,this.errorMessage="Registration failed. Please try again.",console.error("Registration error:",t)}}))}get username(){return this.registerForm.get("username")}get email(){return this.registerForm.get("email")}get password(){return this.registerForm.get("password")}static{this.\u0275fac=function(u){return new(u||e)(g(M),g(C),g(b))}}static{this.\u0275cmp=v({type:e,selectors:[["app-sign-up"]],standalone:!0,features:[y],decls:31,vars:8,consts:[[1,"min-h-screen","flex","items-center","justify-center","bg-gray-50","py-12","px-4","sm:px-6","lg:px-8"],[1,"max-w-md","w-full","space-y-8"],[1,"mt-6","text-center","text-3xl","font-extrabold","text-gray-900"],[1,"mt-2","text-center","text-sm","text-gray-600"],["routerLink","/sign-in",1,"font-medium","text-indigo-600","hover:text-indigo-500"],[1,"mt-8","space-y-6",3,"ngSubmit","formGroup"],[1,"rounded-md","shadow-sm","-space-y-px"],["for","username",1,"sr-only"],["id","username","name","username","type","text","formControlName","username","required","","placeholder","Username",1,"appearance-none","rounded-none","relative","block","w-full","px-3","py-2","border","border-gray-300","placeholder-gray-500","text-gray-900","rounded-t-md","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500","focus:z-10","sm:text-sm"],["class","text-red-600 text-sm mt-1",4,"ngIf"],["for","email",1,"sr-only"],["id","email","name","email","type","email","formControlName","email","required","","placeholder","Email address",1,"appearance-none","rounded-none","relative","block","w-full","px-3","py-2","border","border-gray-300","placeholder-gray-500","text-gray-900","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500","focus:z-10","sm:text-sm"],["for","password",1,"sr-only"],["id","password","name","password","type","password","formControlName","password","required","","placeholder","Password",1,"appearance-none","rounded-none","relative","block","w-full","px-3","py-2","border","border-gray-300","placeholder-gray-500","text-gray-900","rounded-b-md","focus:outline-none","focus:ring-indigo-500","focus:border-indigo-500","focus:z-10","sm:text-sm"],["class","text-red-600 text-sm text-center",4,"ngIf"],["type","submit",1,"group","relative","w-full","flex","justify-center","py-2","px-4","border","border-transparent","text-sm","font-medium","rounded-md","text-white","bg-indigo-600","hover:bg-indigo-700","focus:outline-none","focus:ring-2","focus:ring-offset-2","focus:ring-indigo-500","disabled:opacity-50","disabled:cursor-not-allowed",3,"disabled"],["class","absolute left-0 inset-y-0 flex items-center pl-3",4,"ngIf"],[1,"text-red-600","text-sm","mt-1"],[4,"ngIf"],[1,"text-red-600","text-sm","text-center"],[1,"absolute","left-0","inset-y-0","flex","items-center","pl-3"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24",1,"animate-spin","h-5","w-5","text-white"],["cx","12","cy","12","r","10","stroke","currentColor","stroke-width","4",1,"opacity-25"],["fill","currentColor","d","M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",1,"opacity-75"]],template:function(u,r){u&1&&(i(0,"div",0)(1,"div",1)(2,"div")(3,"h2",2),a(4," Create your JobUp account "),n(),i(5,"p",3),a(6," Or "),i(7,"a",4),a(8," sign in to your existing account "),n()()(),i(9,"form",5),_("ngSubmit",function(){return r.onSubmit()}),i(10,"div",6)(11,"div")(12,"label",7),a(13,"Username"),n(),p(14,"input",8),m(15,V,4,3,"div",9),n(),i(16,"div")(17,"label",10),a(18,"Email"),n(),p(19,"input",11),m(20,G,3,2,"div",9),n(),i(21,"div")(22,"label",12),a(23,"Password"),n(),p(24,"input",13),m(25,B,3,2,"div",9),n()(),m(26,H,2,1,"div",14),i(27,"div")(28,"button",15),m(29,J,4,0,"span",16),a(30),n()()()()()),u&2&&(o(9),l("formGroup",r.registerForm),o(6),l("ngIf",(r.username==null?null:r.username.invalid)&&(r.username==null?null:r.username.touched)),o(5),l("ngIf",(r.email==null?null:r.email.invalid)&&(r.email==null?null:r.email.touched)),o(5),l("ngIf",(r.password==null?null:r.password.invalid)&&(r.password==null?null:r.password.touched)),o(),l("ngIf",r.errorMessage),o(2),l("disabled",r.registerForm.invalid||r.isLoading),o(),l("ngIf",r.isLoading),o(),f(" ",r.isLoading?"Creating account...":"Create account"," "))},dependencies:[S,h,N,F,E,I,U,L,T,q,w]})}}return e})();export{te as SignUpComponent};
