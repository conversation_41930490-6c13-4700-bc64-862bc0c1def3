import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';
import { AuthenticationService, AuthResponseDto, LoginRequestDto, RegisterRequestDto } from '../generated-sources/openapi';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'jobup_token';
  private readonly USER_KEY = 'jobup_user';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());
  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());

  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private authController: AuthenticationService,
    private router: Router
  ) { }

  register(registerData: RegisterRequestDto): Observable<AuthResponseDto> {
    return this.authController.register(registerData).pipe(
      tap(response => this.handleAuthSuccess(response))
    );
  }

  login(loginData: LoginRequestDto): Observable<AuthResponseDto> {
    return this.authController.login(loginData).pipe(
      tap(response => this.handleAuthSuccess(response))
    );
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
    this.router.navigate(['/sign-in']);
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): any {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  hasToken(): boolean {
    return !!this.getToken();
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles?.includes(role) || false;
  }

  private handleAuthSuccess(response: AuthResponseDto): void {
    if (response.token) {
      localStorage.setItem(this.TOKEN_KEY, response.token);
      const userData = {
        roles: response.roles || []
      };
      localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
      this.isAuthenticatedSubject.next(true);
      this.currentUserSubject.next(userData);
      this.router.navigate(['/home']);
    }
  }
}
