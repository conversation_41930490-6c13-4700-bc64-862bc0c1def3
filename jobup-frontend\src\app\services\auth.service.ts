import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap, catchError, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { AuthenticationService, AuthResponseDto, LoginRequestDto, RegisterRequestDto } from '../generated-sources/openapi';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'jobup_token';
  private readonly USER_KEY = 'jobup_user';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());
  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());

  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private authController: AuthenticationService,
    private router: Router
  ) { }

  register(registerData: RegisterRequestDto): Observable<AuthResponseDto> {
    return this.authController.register(registerData).pipe(
      tap(async response => await this.handleAuthSuccess(response))
    );
  }

  login(loginData: LoginRequestDto): Observable<AuthResponseDto> {
    return this.authController.login(loginData).pipe(
      tap(async response => {
        await this.handleAuthSuccess(response);
      }),
      catchError((error: any) => {
        console.error('Login failed:', error);
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
    this.router.navigate(['/sign-in']);
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): any {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  hasToken(): boolean {
    return !!this.getToken();
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles?.includes(role) || false;
  }

  // ✅ NOUVELLES FONCTIONS AJOUTÉES
  getCurrentUserId(): string | null {
    const token = this.getToken();
    if (!token) return null;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub; // Le username/userId est dans 'sub'
    } catch {
      return null;
    }
  }

  refreshUserData(): Observable<any> {
    return this.authController.getCurrentUser().pipe(
      tap(response => {
        const userData = {
          roles: response.roles || []
        };
        localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
        this.currentUserSubject.next(userData);
      })
    );
  }

  isWorker(): boolean {
    return this.hasRole('ROLE_WORKER');
  }

  private async handleAuthSuccess(response: any): Promise<void> {
    let parsedResponse: AuthResponseDto;

    // Handle Blob response (due to OpenAPI client configuration)
    if (response instanceof Blob) {
      try {
        const text = await response.text();
        parsedResponse = JSON.parse(text);
      } catch (error) {
        console.error('Failed to parse Blob response:', error);
        return;
      }
    } else {
      parsedResponse = response;
    }

    if (parsedResponse && parsedResponse.token) {
      localStorage.setItem(this.TOKEN_KEY, parsedResponse.token);

      const userData = {
        roles: parsedResponse.roles || []
      };
      localStorage.setItem(this.USER_KEY, JSON.stringify(userData));

      // Update subjects
      this.isAuthenticatedSubject.next(true);
      this.currentUserSubject.next(userData);

      // Navigate to home
      setTimeout(() => {
        this.router.navigate(['/home']);
      }, 100);
    } else {
      console.error('No token in response:', parsedResponse);
    }
  }
}
