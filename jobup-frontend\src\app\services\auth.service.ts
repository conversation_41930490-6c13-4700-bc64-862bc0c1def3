import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap, catchError, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { AuthenticationService, AuthResponseDto, LoginRequestDto, RegisterRequestDto } from '../generated-sources/openapi';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'jobup_token';
  private readonly USER_KEY = 'jobup_user';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());
  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());

  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private authController: AuthenticationService,
    private router: Router
  ) { }

  register(registerData: RegisterRequestDto): Observable<AuthResponseDto> {
    return this.authController.register(registerData).pipe(
      tap(async response => await this.handleAuthSuccess(response))
    );
  }

  login(loginData: LoginRequestDto): Observable<AuthResponseDto> {
    console.log('Attempting login with:', loginData);
    // Use the default call which should work with proper response handling
    return this.authController.login(loginData).pipe(
      tap(async response => {
        console.log('Login successful:', response);
        await this.handleAuthSuccess(response);
      }),
      catchError((error: any) => {
        console.error('Login failed:', error);
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
    this.router.navigate(['/sign-in']);
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): any {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  hasToken(): boolean {
    const token = this.getToken();
    const hasToken = !!token;
    console.log('Has token check:', hasToken, 'Token:', token ? 'exists' : 'null');
    return hasToken;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles?.includes(role) || false;
  }

  private async handleAuthSuccess(response: any): Promise<void> {
    console.log('Auth success response:', response);
    console.log('Response type:', typeof response);
    console.log('Response constructor:', response?.constructor?.name);

    let parsedResponse: AuthResponseDto;

    // Handle Blob response
    if (response instanceof Blob) {
      console.log('Response is Blob, parsing...');
      try {
        const text = await response.text();
        console.log('Blob text content:', text);
        parsedResponse = JSON.parse(text);
        console.log('Parsed response:', parsedResponse);
      } catch (error) {
        console.error('Failed to parse Blob response:', error);
        return;
      }
    } else {
      parsedResponse = response;
    }

    console.log('Final parsed response token:', parsedResponse.token);
    console.log('Final parsed response roles:', parsedResponse.roles);

    if (parsedResponse && parsedResponse.token) {
      console.log('Storing token:', parsedResponse.token);
      localStorage.setItem(this.TOKEN_KEY, parsedResponse.token);

      const userData = {
        roles: parsedResponse.roles || []
      };
      console.log('Storing user data:', userData);
      localStorage.setItem(this.USER_KEY, JSON.stringify(userData));

      // Update subjects
      this.isAuthenticatedSubject.next(true);
      this.currentUserSubject.next(userData);

      console.log('Token stored, checking retrieval:', this.getToken());
      console.log('User data stored, checking retrieval:', this.getCurrentUser());

      // Navigate to home with a small delay to ensure state is updated
      setTimeout(() => {
        console.log('Navigating to /home...');
        this.router.navigate(['/home']).then(success => {
          console.log('Navigation result:', success);
          console.log('Current URL after navigation:', this.router.url);
        });
      }, 100);
    } else {
      console.error('No token in parsed response:', parsedResponse);
    }
  }
}
