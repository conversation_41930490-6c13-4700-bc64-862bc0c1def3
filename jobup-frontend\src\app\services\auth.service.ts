import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';
import { AuthenticationService, AuthResponseDto, LoginRequestDto, RegisterRequestDto } from '../generated-sources/openapi';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'jobup_token';
  private readonly USER_KEY = 'jobup_user';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());
  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());

  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private authController: AuthenticationService,
    private router: Router
  ) { }

  register(registerData: RegisterRequestDto): Observable<AuthResponseDto> {
    return this.authController.register(registerData).pipe(
      tap(response => this.handleAuthSuccess(response))
    );
  }

  login(loginData: LoginRequestDto): Observable<AuthResponseDto> {
    return this.authController.login(loginData).pipe(
      tap(response => this.handleAuthSuccess(response))
    );
  }

  logout(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
    this.router.navigate(['/sign-in']);
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getCurrentUser(): any {
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  hasToken(): boolean {
    const token = this.getToken();
    const hasToken = !!token;
    console.log('Has token check:', hasToken, 'Token:', token ? 'exists' : 'null');
    return hasToken;
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.roles?.includes(role) || false;
  }

  private handleAuthSuccess(response: AuthResponseDto): void {
    console.log('Auth success response:', response);
    console.log('Response token:', response.token);
    console.log('Response roles:', response.roles);

    if (response && response.token) {
      console.log('Storing token:', response.token);
      localStorage.setItem(this.TOKEN_KEY, response.token);

      const userData = {
        roles: response.roles || []
      };
      console.log('Storing user data:', userData);
      localStorage.setItem(this.USER_KEY, JSON.stringify(userData));

      // Update subjects
      this.isAuthenticatedSubject.next(true);
      this.currentUserSubject.next(userData);

      console.log('Token stored, checking retrieval:', this.getToken());
      console.log('User data stored, checking retrieval:', this.getCurrentUser());

      // Navigate to home with a small delay to ensure state is updated
      setTimeout(() => {
        console.log('Navigating to /home...');
        this.router.navigate(['/home']).then(success => {
          console.log('Navigation result:', success);
          console.log('Current URL after navigation:', this.router.url);
        });
      }, 100);
    } else {
      console.error('No token in response:', response);
    }
  }
}
