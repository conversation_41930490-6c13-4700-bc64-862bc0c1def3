import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthenticationService } from './generated-sources/openapi';

@Component({
  selector: 'app-test-auth',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div>
      <h2>Auth Test</h2>
      <button (click)="testLogin()">Test Login</button>
      <button (click)="testRegister()">Test Register</button>
      <div *ngIf="result">
        <h3>Result:</h3>
        <pre>{{ result | json }}</pre>
      </div>
      <div *ngIf="error">
        <h3>Error:</h3>
        <pre>{{ error | json }}</pre>
      </div>
    </div>
  `
})
export class TestAuthComponent {
  result: any = null;
  error: any = null;

  constructor(private authService: AuthenticationService) {}

  testLogin() {
    console.log('Testing login...');
    console.log('Auth service config:', this.authService);
    
    this.authService.login({
      username: 'testuser2',
      password: 'password123'
    }).subscribe({
      next: (response) => {
        console.log('Login success:', response);
        this.result = response;
        this.error = null;
      },
      error: (error) => {
        console.error('Login error:', error);
        this.error = error;
        this.result = null;
      }
    });
  }

  testRegister() {
    console.log('Testing register...');
    
    this.authService.register({
      username: 'testuser3',
      email: '<EMAIL>',
      password: 'password123'
    }).subscribe({
      next: (response) => {
        console.log('Register success:', response);
        this.result = response;
        this.error = null;
      },
      error: (error) => {
        console.error('Register error:', error);
        this.error = error;
        this.result = null;
      }
    });
  }
}
