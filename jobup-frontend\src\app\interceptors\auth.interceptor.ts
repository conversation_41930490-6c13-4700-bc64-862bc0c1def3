import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { AuthService } from '../services/auth.service';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const token = authService.getToken();

  console.log('Interceptor - Request URL:', req.url);
  console.log('Interceptor - Has token:', !!token);

  // Skip adding token for auth endpoints
  if (req.url.includes('/auth/')) {
    console.log('Interceptor - Skipping auth header for auth endpoint');
    return next(req);
  }

  // Add Authorization header if token exists
  if (token) {
    console.log('Interceptor - Adding auth header');
    const authReq = req.clone({
      headers: req.headers.set('Authorization', `Bearer ${token}`)
    });
    return next(authReq);
  }

  console.log('Interceptor - No token, proceeding without auth header');
  return next(req);
};
