import {
  APP_BASE_HREF,
  AsyncPipe,
  BrowserPlatformLocation,
  CommonModule,
  CurrencyPipe,
  DATE_PIPE_DEFAULT_OPTIONS,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DOCUMENT,
  DatePipe,
  DecimalPipe,
  DomAdapter,
  FormStyle,
  FormatWidth,
  HashLocationStrategy,
  I18nPluralPipe,
  I18nSelectPipe,
  IMAGE_LOADER,
  JsonPipe,
  KeyValuePipe,
  LOCATION_INITIALIZED,
  Location,
  LocationStrategy,
  LowerCasePipe,
  NgClass,
  NgComponentOutlet,
  NgForOf,
  NgForOfContext,
  NgIf,
  NgIfContext,
  NgLocaleLocalization,
  NgLocalization,
  NgOptimizedImage,
  NgPlural,
  NgPluralCase,
  NgStyle,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
  NgTemplateOutlet,
  NullViewportScroller,
  NumberFormatStyle,
  NumberSymbol,
  PLATFORM_BROWSER_ID,
  PLATFORM_SERVER_ID,
  PLATFORM_WORKER_APP_ID,
  PLATFORM_WORKER_UI_ID,
  PRECONNECT_CHECK_BLOCKLIST,
  PathLocationStrategy,
  PercentPipe,
  PlatformLocation,
  PlatformNavigation,
  Plural,
  SlicePipe,
  TitleCasePipe,
  TranslationWidth,
  UpperCasePipe,
  VERSION,
  ViewportScroller,
  WeekDay,
  XhrFactory,
  formatCurrency,
  formatDate,
  formatNumber,
  formatPercent,
  getCurrencySymbol,
  getDOM,
  getLocaleCurrencyCode,
  getLocaleCurrencyName,
  getLocaleCurrencySymbol,
  getLocaleDateFormat,
  getLocaleDateTimeFormat,
  getLocaleDayNames,
  getLocaleDayPeriods,
  getLocaleDirection,
  getLocaleEraNames,
  getLocaleExtraDayPeriodRules,
  getLocaleExtraDayPeriods,
  getLocaleFirstDayOfWeek,
  getLocaleId,
  getLocaleMonthNames,
  getLocaleNumberFormat,
  getLocaleNumberSymbol,
  getLocalePluralCase,
  getLocaleTimeFormat,
  getLocaleWeekEndRange,
  getNumberOfCurrencyDigits,
  isPlatformBrowser,
  isPlatformServer,
  isPlatformWorkerApp,
  isPlatformWorkerUi,
  normalizeQueryParams,
  parseCookieValue,
  provideCloudflareLoader,
  provideCloudinaryLoader,
  provideImageKitLoader,
  provideImgixLoader,
  provideNetlifyLoader,
  registerLocaleData,
  setRootDomAdapter
} from "./chunk-T76FZRMF.js";
import {
  IMAGE_CONFIG
} from "./chunk-6JJ7KVRE.js";
import "./chunk-T4QU4GDF.js";
export {
  APP_BASE_HREF,
  AsyncPipe,
  BrowserPlatformLocation,
  CommonModule,
  CurrencyPipe,
  DATE_PIPE_DEFAULT_OPTIONS,
  DATE_PIPE_DEFAULT_TIMEZONE,
  DOCUMENT,
  DatePipe,
  DecimalPipe,
  FormStyle,
  FormatWidth,
  HashLocationStrategy,
  I18nPluralPipe,
  I18nSelectPipe,
  IMAGE_CONFIG,
  IMAGE_LOADER,
  JsonPipe,
  KeyValuePipe,
  LOCATION_INITIALIZED,
  Location,
  LocationStrategy,
  LowerCasePipe,
  NgClass,
  NgComponentOutlet,
  NgForOf as NgFor,
  NgForOf,
  NgForOfContext,
  NgIf,
  NgIfContext,
  NgLocaleLocalization,
  NgLocalization,
  NgOptimizedImage,
  NgPlural,
  NgPluralCase,
  NgStyle,
  NgSwitch,
  NgSwitchCase,
  NgSwitchDefault,
  NgTemplateOutlet,
  NumberFormatStyle,
  NumberSymbol,
  PRECONNECT_CHECK_BLOCKLIST,
  PathLocationStrategy,
  PercentPipe,
  PlatformLocation,
  Plural,
  SlicePipe,
  TitleCasePipe,
  TranslationWidth,
  UpperCasePipe,
  VERSION,
  ViewportScroller,
  WeekDay,
  XhrFactory,
  formatCurrency,
  formatDate,
  formatNumber,
  formatPercent,
  getCurrencySymbol,
  getLocaleCurrencyCode,
  getLocaleCurrencyName,
  getLocaleCurrencySymbol,
  getLocaleDateFormat,
  getLocaleDateTimeFormat,
  getLocaleDayNames,
  getLocaleDayPeriods,
  getLocaleDirection,
  getLocaleEraNames,
  getLocaleExtraDayPeriodRules,
  getLocaleExtraDayPeriods,
  getLocaleFirstDayOfWeek,
  getLocaleId,
  getLocaleMonthNames,
  getLocaleNumberFormat,
  getLocaleNumberSymbol,
  getLocalePluralCase,
  getLocaleTimeFormat,
  getLocaleWeekEndRange,
  getNumberOfCurrencyDigits,
  isPlatformBrowser,
  isPlatformServer,
  isPlatformWorkerApp,
  isPlatformWorkerUi,
  provideCloudflareLoader,
  provideCloudinaryLoader,
  provideImageKitLoader,
  provideImgixLoader,
  provideNetlifyLoader,
  registerLocaleData,
  DomAdapter as ɵDomAdapter,
  NullViewportScroller as ɵNullViewportScroller,
  PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID,
  PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID,
  PLATFORM_WORKER_APP_ID as ɵPLATFORM_WORKER_APP_ID,
  PLATFORM_WORKER_UI_ID as ɵPLATFORM_WORKER_UI_ID,
  PlatformNavigation as ɵPlatformNavigation,
  getDOM as ɵgetDOM,
  normalizeQueryParams as ɵnormalizeQueryParams,
  parseCookieValue as ɵparseCookieValue,
  setRootDomAdapter as ɵsetRootDomAdapter
};
//# sourceMappingURL=@angular_common.js.map
