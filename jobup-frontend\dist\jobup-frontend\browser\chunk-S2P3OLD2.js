import{a as S,b as m,c as I,d as F,e as N,f as T,g as W,h as j,i as D,j as z,l as L,m as V}from"./chunk-XXRJEYVX.js";import{b as w,d as O}from"./chunk-H277OY4J.js";import{Ca as p,Ea as l,Fa as u,Ga as n,Ha as r,Ia as g,Ja as v,Ka as _,La as c,Ma as i,Oa as f,R as b,Ra as k,U as C,aa as x,ba as h,ib as y,jb as E,mb as P,ra as o,ub as M}from"./chunk-T3NICLM4.js";function q(t,d){t&1&&(n(0,"div",10),g(1,"div",11),n(2,"p"),i(3,"Loading worker data..."),r()())}function A(t,d){if(t&1){let e=v();n(0,"div",12)(1,"i",13),i(2,"\u26A0"),r(),i(3),n(4,"button",14),_("click",function(){x(e);let s=c();return h(s.loadWorkerData())}),i(5,"Retry"),r()()}if(t&2){let e=c();o(3),f(" ",e.loadError," ")}}function B(t,d){t&1&&(n(0,"div",15)(1,"i",16),i(2,"\u2713"),r(),i(3," Worker profile updated successfully! Redirecting to profile page... "),r())}function G(t,d){if(t&1&&(n(0,"div",12)(1,"i",13),i(2,"\u26A0"),r(),i(3),r()),t&2){let e=c();o(3),f(" ",e.submitError," ")}}function R(t,d){if(t&1&&(n(0,"div",37),i(1),r()),t&2){let e=c(2);o(),f(" ",e.getFieldError("fullName")," ")}}function $(t,d){if(t&1&&(n(0,"option",38),i(1),r()),t&2){let e=d.$implicit;l("value",e),o(),f(" ",e," ")}}function U(t,d){if(t&1&&(n(0,"div",37),i(1),r()),t&2){let e=c(2);o(),f(" ",e.getFieldError("jobType")," ")}}function J(t,d){if(t&1&&(n(0,"div",37),i(1),r()),t&2){let e=c(2);o(),f(" ",e.getFieldError("phoneNumber")," ")}}function H(t,d){if(t&1&&(n(0,"div",37),i(1),r()),t&2){let e=c(2);o(),f(" ",e.getFieldError("location")," ")}}function Y(t,d){if(t&1&&(n(0,"div",37),i(1),r()),t&2){let e=c(2);o(),f(" ",e.getFieldError("description")," ")}}function K(t,d){t&1&&(n(0,"span"),i(1,"Update Profile"),r())}function Q(t,d){t&1&&(n(0,"span"),g(1,"i",11),i(2," Updating Profile... "),r())}function X(t,d){if(t&1){let e=v();n(0,"form",17),_("ngSubmit",function(){x(e);let s=c();return h(s.onSubmit())}),n(1,"div",18)(2,"label",19),i(3," Full Name "),n(4,"span",20),i(5,"*"),r()(),g(6,"input",21),p(7,R,2,1,"div",22),r(),n(8,"div",18)(9,"label",23),i(10," Job Type "),n(11,"span",20),i(12,"*"),r()(),n(13,"select",24)(14,"option",25),i(15,"Select your job type"),r(),p(16,$,2,2,"option",26),r(),p(17,U,2,1,"div",22),r(),n(18,"div",18)(19,"label",27),i(20," Phone Number "),n(21,"span",20),i(22,"*"),r()(),g(23,"input",28),p(24,J,2,1,"div",22),r(),n(25,"div",18)(26,"label",29),i(27," Location "),n(28,"span",20),i(29,"*"),r()(),g(30,"input",30),p(31,H,2,1,"div",22),r(),n(32,"div",18)(33,"label",31),i(34," Description "),n(35,"span",20),i(36,"*"),r()(),g(37,"textarea",32),p(38,Y,2,1,"div",22),r(),n(39,"div",33)(40,"button",34),_("click",function(){x(e);let s=c();return h(s.goBack())}),i(41," Cancel "),r(),n(42,"button",35),p(43,K,2,0,"span",36)(44,Q,3,0,"span",36),r()()()}if(t&2){let e=c();l("formGroup",e.workerForm),o(6),u("error",e.isFieldInvalid("fullName")),o(),l("ngIf",e.isFieldInvalid("fullName")),o(6),u("error",e.isFieldInvalid("jobType")),o(3),l("ngForOf",e.jobTypes),o(),l("ngIf",e.isFieldInvalid("jobType")),o(6),u("error",e.isFieldInvalid("phoneNumber")),o(),l("ngIf",e.isFieldInvalid("phoneNumber")),o(6),u("error",e.isFieldInvalid("location")),o(),l("ngIf",e.isFieldInvalid("location")),o(6),u("error",e.isFieldInvalid("description")),o(),l("ngIf",e.isFieldInvalid("description")),o(2),l("disabled",e.isSubmitting),o(2),u("loading",e.isSubmitting),l("disabled",e.isSubmitting),o(),l("ngIf",!e.isSubmitting),o(),l("ngIf",e.isSubmitting)}}var se=(()=>{class t{constructor(){this.fb=b(L),this.route=b(w),this.router=b(O),this.workerService=b(M),this.workerId=null,this.isLoading=!1,this.isSubmitting=!1,this.submitError=null,this.submitSuccess=!1,this.loadError=null,this.jobTypes=["Plumber","Electrician","Carpenter","Painter","Cleaner","Gardener","Mechanic","Cook","Driver","Handyman","Other"],this.workerForm=this.fb.group({fullName:["",[m.required,m.minLength(2)]],jobType:["",m.required],phoneNumber:["",[m.required,m.pattern(/^\+?[\d\s\-\(\)]+$/)]],location:["",[m.required,m.minLength(2)]],description:["",[m.required,m.minLength(10)]]})}ngOnInit(){this.workerId=this.route.snapshot.paramMap.get("id"),this.workerId?this.loadWorkerData():this.loadError="Worker ID not found"}loadWorkerData(){this.workerId&&(this.isLoading=!0,this.loadError=null,this.workerService.getWorkerById(this.workerId).subscribe({next:e=>{this.populateForm(e),this.isLoading=!1},error:e=>{console.error("Error loading worker data:",e),this.loadError="Failed to load worker data. Please try again.",this.isLoading=!1}}))}populateForm(e){this.workerForm.patchValue({fullName:e.fullName||"",jobType:e.jobType||"",phoneNumber:e.phoneNumber||"",location:e.location||"",description:e.description||""})}onSubmit(){if(this.workerForm.valid&&!this.isSubmitting&&this.workerId){this.isSubmitting=!0,this.submitError=null,this.submitSuccess=!1;let e={fullName:this.workerForm.value.fullName,jobType:this.workerForm.value.jobType,phoneNumber:this.workerForm.value.phoneNumber,location:this.workerForm.value.location,description:this.workerForm.value.description};this.workerService.updateWorker(this.workerId,e).subscribe({next:a=>{console.log("Worker updated successfully:",a),this.submitSuccess=!0,this.isSubmitting=!1,setTimeout(()=>{this.router.navigate(["/workers",this.workerId])},2e3)},error:a=>{console.error("Error updating worker:",a),this.submitError="Failed to update worker profile. Please try again.",this.isSubmitting=!1}})}else Object.keys(this.workerForm.controls).forEach(e=>{this.workerForm.get(e)?.markAsTouched()})}goBack(){this.workerId?this.router.navigate(["/workers",this.workerId]):this.router.navigate(["/workers"])}isFieldInvalid(e){let a=this.workerForm.get(e);return!!(a&&a.invalid&&(a.dirty||a.touched))}getFieldError(e){let a=this.workerForm.get(e);if(a&&a.errors&&(a.dirty||a.touched)){if(a.errors.required)return`${this.getFieldDisplayName(e)} is required`;if(a.errors.minlength)return`${this.getFieldDisplayName(e)} must be at least ${a.errors.minlength.requiredLength} characters`;if(a.errors.pattern)return"Please enter a valid phone number"}return""}getFieldDisplayName(e){return{fullName:"Full Name",jobType:"Job Type",phoneNumber:"Phone Number",location:"Location",description:"Description"}[e]||e}static{this.\u0275fac=function(a){return new(a||t)}}static{this.\u0275cmp=C({type:t,selectors:[["app-worker-edit"]],standalone:!0,features:[k],decls:17,vars:5,consts:[[1,"worker-edit-container"],[1,"navigation"],[1,"back-btn",3,"click"],[1,"back-icon"],[1,"form-wrapper"],[1,"header"],["class","loading-container",4,"ngIf"],["class","alert alert-error",4,"ngIf"],["class","alert alert-success",4,"ngIf"],["class","worker-form",3,"formGroup","ngSubmit",4,"ngIf"],[1,"loading-container"],[1,"loading-spinner"],[1,"alert","alert-error"],[1,"error-icon"],[1,"retry-btn",3,"click"],[1,"alert","alert-success"],[1,"success-icon"],[1,"worker-form",3,"ngSubmit","formGroup"],[1,"form-group"],["for","fullName",1,"form-label"],[1,"required"],["type","text","id","fullName","formControlName","fullName","placeholder","Enter your full name",1,"form-input"],["class","error-message",4,"ngIf"],["for","jobType",1,"form-label"],["id","jobType","formControlName","jobType",1,"form-select"],["value",""],[3,"value",4,"ngFor","ngForOf"],["for","phoneNumber",1,"form-label"],["type","tel","id","phoneNumber","formControlName","phoneNumber","placeholder","Enter your phone number",1,"form-input"],["for","location",1,"form-label"],["type","text","id","location","formControlName","location","placeholder","Enter your city or area",1,"form-input"],["for","description",1,"form-label"],["id","description","formControlName","description","placeholder","Describe your skills, experience, and services you offer...","rows","4",1,"form-textarea"],[1,"form-actions"],["type","button",1,"cancel-btn",3,"click","disabled"],["type","submit",1,"submit-btn",3,"disabled"],[4,"ngIf"],[1,"error-message"],[3,"value"]],template:function(a,s){a&1&&(n(0,"div",0)(1,"div",1)(2,"button",2),_("click",function(){return s.goBack()}),n(3,"i",3),i(4,"\u2190"),r(),i(5," Back to Profile "),r()(),n(6,"div",4)(7,"div",5)(8,"h1"),i(9,"Edit Worker Profile"),r(),n(10,"p"),i(11,"Update your profile information to keep it current and accurate."),r()(),p(12,q,4,0,"div",6)(13,A,6,1,"div",7)(14,B,4,0,"div",8)(15,G,4,1,"div",7)(16,X,45,23,"form",9),r()()),a&2&&(o(12),l("ngIf",s.isLoading),o(),l("ngIf",s.loadError),o(),l("ngIf",s.submitSuccess),o(),l("ngIf",s.submitError),o(),l("ngIf",!s.isLoading&&!s.loadError))},dependencies:[P,y,E,V,N,D,z,S,j,I,F,T,W],styles:[".worker-edit-container[_ngcontent-%COMP%]{min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:2rem 1rem}.navigation[_ngcontent-%COMP%]{margin-bottom:2rem}.back-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:.75rem 1rem;border-radius:8px;font-size:.9rem;cursor:pointer;transition:all .2s ease;display:inline-flex;align-items:center;gap:.5rem}.back-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.back-icon[_ngcontent-%COMP%]{font-size:1.2rem}.form-wrapper[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 20px 40px #0000001a;padding:2.5rem;width:100%;max-width:600px;margin:0 auto}.header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{color:#2d3748;font-size:2rem;font-weight:700;margin-bottom:.5rem}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#718096;font-size:1rem;line-height:1.5}.loading-container[_ngcontent-%COMP%]{text-align:center;padding:3rem}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #e2e8f0;border-top:4px solid #667eea;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0 auto 1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.alert[_ngcontent-%COMP%]{padding:1rem;border-radius:8px;margin-bottom:1.5rem;display:flex;align-items:center;gap:.5rem}.alert-success[_ngcontent-%COMP%]{background-color:#f0fff4;border:1px solid #9ae6b4;color:#276749}.alert-error[_ngcontent-%COMP%]{background-color:#fed7d7;border:1px solid #feb2b2;color:#c53030}.success-icon[_ngcontent-%COMP%], .error-icon[_ngcontent-%COMP%]{font-weight:700;font-size:1.2rem}.retry-btn[_ngcontent-%COMP%]{background:#c53030;color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;font-size:.9rem;cursor:pointer;margin-left:auto}.retry-btn[_ngcontent-%COMP%]:hover{background:#9c2626}.worker-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:1.5rem}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column}.form-label[_ngcontent-%COMP%]{font-weight:600;color:#2d3748;margin-bottom:.5rem;font-size:.95rem}.required[_ngcontent-%COMP%]{color:#e53e3e}.form-input[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%], .form-textarea[_ngcontent-%COMP%]{padding:.75rem;border:2px solid #e2e8f0;border-radius:8px;font-size:1rem;transition:all .2s ease;background-color:#fff}.form-input[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus, .form-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#667eea;box-shadow:0 0 0 3px #667eea1a}.form-input.error[_ngcontent-%COMP%], .form-select.error[_ngcontent-%COMP%], .form-textarea.error[_ngcontent-%COMP%]{border-color:#e53e3e;box-shadow:0 0 0 3px #e53e3e1a}.form-textarea[_ngcontent-%COMP%]{resize:vertical;min-height:100px}.error-message[_ngcontent-%COMP%]{color:#e53e3e;font-size:.875rem;margin-top:.25rem}.form-actions[_ngcontent-%COMP%]{display:flex;gap:1rem;margin-top:1rem}.cancel-btn[_ngcontent-%COMP%]{flex:1;background:#f7fafc;color:#4a5568;border:2px solid #e2e8f0;padding:1rem 2rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all .2s ease}.cancel-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#edf2f7;border-color:#cbd5e0}.cancel-btn[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.submit-btn[_ngcontent-%COMP%]{flex:2;background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:1rem 2rem;border-radius:8px;font-size:1.1rem;font-weight:600;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;gap:.5rem}.submit-btn[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 10px 20px #667eea4d}.submit-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed;transform:none}.submit-btn.loading[_ngcontent-%COMP%]{background:#a0aec0}.submit-btn[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:16px;height:16px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin:0}@media (max-width: 768px){.worker-edit-container[_ngcontent-%COMP%]{padding:1rem}.form-wrapper[_ngcontent-%COMP%]{padding:1.5rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem}.header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem}.form-actions[_ngcontent-%COMP%]{flex-direction:column}.cancel-btn[_ngcontent-%COMP%], .submit-btn[_ngcontent-%COMP%]{flex:none}}@media (max-width: 480px){.form-wrapper[_ngcontent-%COMP%]{padding:1rem}.header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.25rem}}"]})}}return t})();export{se as WorkerEditComponent};
