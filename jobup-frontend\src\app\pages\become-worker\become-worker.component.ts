import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { WorkerControllerService, WorkerCreateDto } from '../../generated-sources/openapi';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-become-worker',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './become-worker.component.html',
  styleUrl: './become-worker.component.css'
})
export class BecomeWorkerComponent {
  private fb = inject(FormBuilder);
  private workerService = inject(WorkerControllerService);
  private authService = inject(AuthService);
  private router = inject(Router);

  isLoading = false;
  error: string | null = null;

  workerForm = this.fb.group({
    jobType: ['', Validators.required],
    phoneNumber: ['', Validators.required],
    location: ['', Validators.required],
    description: ['']
  });

  jobTypes = [
    'Plumber', 'Electrician', 'Carpenter', 'Painter',
    'Cleaner', 'Gardener', 'Mechanic', 'Cook',
    'Driver', 'Handyman', 'Other'
  ];

  onSubmit(): void {
    if (this.workerForm.valid) {
      this.isLoading = true;
      this.error = null;

      const formValue = this.workerForm.value;
      const workerData: WorkerCreateDto = {
        jobType: formValue.jobType || undefined,
        phoneNumber: formValue.phoneNumber || undefined,
        location: formValue.location || undefined,
        description: formValue.description || undefined
      };

      this.workerService.createWorker(workerData).subscribe({
        next: () => {
          this.authService.refreshUserData().subscribe(() => {
            this.router.navigate(['/home']);
          });
        },
        error: (error) => {
          console.error('Error creating worker profile:', error);
          this.error = 'Failed to create worker profile. Please try again.';
          this.isLoading = false;
        }
      });
    }
  }

  onCancel(): void {
    this.router.navigate(['/home']);
  }
}
