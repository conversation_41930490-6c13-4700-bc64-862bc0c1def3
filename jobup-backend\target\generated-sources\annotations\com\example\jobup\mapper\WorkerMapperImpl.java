package com.example.jobup.mapper;

import com.example.jobup.dto.WorkerCreateDto;
import com.example.jobup.dto.WorkerResponseDto;
import com.example.jobup.dto.WorkerUpdateDto;
import com.example.jobup.entities.Worker;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:09:01+0100",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class WorkerMapperImpl implements WorkerMapper {

    @Override
    public WorkerResponseDto toResponseDto(Worker worker) {
        if ( worker == null ) {
            return null;
        }

        WorkerResponseDto.WorkerResponseDtoBuilder workerResponseDto = WorkerResponseDto.builder();

        workerResponseDto.description( worker.getDescription() );
        workerResponseDto.fullName( worker.getFullName() );
        workerResponseDto.id( worker.getId() );
        workerResponseDto.jobType( worker.getJobType() );
        workerResponseDto.location( worker.getLocation() );
        workerResponseDto.phoneNumber( worker.getPhoneNumber() );
        workerResponseDto.rating( worker.getRating() );

        return workerResponseDto.build();
    }

    @Override
    public Worker toEntity(WorkerCreateDto dto) {
        if ( dto == null ) {
            return null;
        }

        Worker.WorkerBuilder worker = Worker.builder();

        worker.description( dto.getDescription() );
        worker.fullName( dto.getFullName() );
        worker.jobType( dto.getJobType() );
        worker.location( dto.getLocation() );
        worker.phoneNumber( dto.getPhoneNumber() );

        return worker.build();
    }

    @Override
    public void updateWorkerFromDto(WorkerUpdateDto dto, Worker entity) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getDescription() != null ) {
            entity.setDescription( dto.getDescription() );
        }
        if ( dto.getFullName() != null ) {
            entity.setFullName( dto.getFullName() );
        }
        if ( dto.getId() != null ) {
            entity.setId( dto.getId() );
        }
        if ( dto.getJobType() != null ) {
            entity.setJobType( dto.getJobType() );
        }
        if ( dto.getLocation() != null ) {
            entity.setLocation( dto.getLocation() );
        }
        if ( dto.getPhoneNumber() != null ) {
            entity.setPhoneNumber( dto.getPhoneNumber() );
        }
    }
}
