package com.example.jobup.mapper;

import com.example.jobup.dto.WorkerCreateDto;
import com.example.jobup.dto.WorkerResponseDto;
import com.example.jobup.dto.WorkerUpdateDto;
import com.example.jobup.entities.Worker;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T10:42:55+0100",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Amazon.com Inc.)"
)
@Component
public class WorkerMapperImpl implements WorkerMapper {

    @Override
    public WorkerResponseDto toResponseDto(Worker worker) {
        if ( worker == null ) {
            return null;
        }

        WorkerResponseDto.WorkerResponseDtoBuilder workerResponseDto = WorkerResponseDto.builder();

        workerResponseDto.id( worker.getId() );
        workerResponseDto.fullName( worker.getFullName() );
        workerResponseDto.jobType( worker.getJobType() );
        workerResponseDto.phoneNumber( worker.getPhoneNumber() );
        workerResponseDto.location( worker.getLocation() );
        workerResponseDto.rating( worker.getRating() );
        workerResponseDto.description( worker.getDescription() );

        return workerResponseDto.build();
    }

    @Override
    public Worker toEntity(WorkerCreateDto dto) {
        if ( dto == null ) {
            return null;
        }

        Worker.WorkerBuilder worker = Worker.builder();

        worker.fullName( dto.getFullName() );
        worker.jobType( dto.getJobType() );
        worker.phoneNumber( dto.getPhoneNumber() );
        worker.location( dto.getLocation() );
        worker.description( dto.getDescription() );

        return worker.build();
    }

    @Override
    public void updateWorkerFromDto(WorkerUpdateDto dto, Worker entity) {
        if ( dto == null ) {
            return;
        }

        if ( dto.getId() != null ) {
            entity.setId( dto.getId() );
        }
        if ( dto.getFullName() != null ) {
            entity.setFullName( dto.getFullName() );
        }
        if ( dto.getJobType() != null ) {
            entity.setJobType( dto.getJobType() );
        }
        if ( dto.getPhoneNumber() != null ) {
            entity.setPhoneNumber( dto.getPhoneNumber() );
        }
        if ( dto.getLocation() != null ) {
            entity.setLocation( dto.getLocation() );
        }
        if ( dto.getDescription() != null ) {
            entity.setDescription( dto.getDescription() );
        }
    }
}
