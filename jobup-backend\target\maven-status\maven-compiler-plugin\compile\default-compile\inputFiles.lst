C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\services\AuthService.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\RegisterRequestDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\mapper\WorkerMapper.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\AuthResponseDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\entities\Role.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\controller\WorkerController.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\WorkerUpdateDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\config\SecurityConfig.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\JobUpApplication.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\repositories\UserRepository.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\entities\Worker.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\WorkerResponseDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\LoginRequestDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\entities\User.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\services\IWorkerService.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\services\WorkerService.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\services\UserDetailsServiceImpl.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\controller\AuthController.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\repositories\WorkerRepo.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\dto\WorkerCreateDto.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\services\JwtUtil.java
C:\Users\<USER>\OneDrive\Desktop\projet de stage 4ème\jobup\jobup-backend\src\main\java\com\example\jobup\config\JwtAuthenticationFilter.java
