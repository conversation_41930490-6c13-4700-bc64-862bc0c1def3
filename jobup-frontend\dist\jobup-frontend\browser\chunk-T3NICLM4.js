var zu=Object.defineProperty,Wu=Object.defineProperties;var Gu=Object.getOwnPropertyDescriptors;var Wi=Object.getOwnPropertySymbols;var qu=Object.prototype.hasOwnProperty,Yu=Object.prototype.propertyIsEnumerable;var Gi=(e,t,n)=>t in e?zu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Je=(e,t)=>{for(var n in t||={})qu.call(t,n)&&Gi(e,n,t[n]);if(Wi)for(var n of Wi(t))Yu.call(t,n)&&Gi(e,n,t[n]);return e},It=(e,t)=>Wu(e,Gu(t));var qi=null;var Er=1,Yi=Symbol("SIGNAL");function b(e){let t=qi;return qi=e,t}var Zi={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Zu(e){if(!(Mr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Er)){if(!e.producerMustRecompute(e)&&!Cr(e)){e.dirty=!1,e.lastCleanEpoch=Er;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Er}}function Qi(e){return e&&(e.nextProducerIndex=0),b(e)}function Ji(e,t){if(b(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Mr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)br(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Cr(e){Wt(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Zu(n),r!==n.version))return!0}return!1}function Ki(e){if(Wt(e),Mr(e))for(let t=0;t<e.producerNode.length;t++)br(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function br(e,t){if(Qu(e),Wt(e),e.liveConsumerNode.length===1)for(let r=0;r<e.producerNode.length;r++)br(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Wt(o),o.producerIndexOfThis[r]=t}}function Mr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Wt(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Qu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ju(){throw new Error}var Ku=Ju;function Xi(e){Ku=e}function y(e){return typeof e=="function"}function Ke(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var Gt=Ke(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Et(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var k=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(y(r))try{r()}catch(i){t=i instanceof Gt?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{es(i)}catch(s){t=t??[],s instanceof Gt?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Gt(t)}}add(t){var n;if(t&&t!==this)if(this.closed)es(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Et(n,t)}remove(t){let{_finalizers:n}=this;n&&Et(n,t),t instanceof e&&t._removeParent(this)}};k.EMPTY=(()=>{let e=new k;return e.closed=!0,e})();var _r=k.EMPTY;function qt(e){return e instanceof k||e&&"closed"in e&&y(e.remove)&&y(e.add)&&y(e.unsubscribe)}function es(e){y(e)?e():e.unsubscribe()}var ee={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Xe={setTimeout(e,t,...n){let{delegate:r}=Xe;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Xe;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Yt(e){Xe.setTimeout(()=>{let{onUnhandledError:t}=ee;if(t)t(e);else throw e})}function Ct(){}var ts=Tr("C",void 0,void 0);function ns(e){return Tr("E",void 0,e)}function rs(e){return Tr("N",e,void 0)}function Tr(e,t,n){return{kind:e,value:t,error:n}}var Se=null;function et(e){if(ee.useDeprecatedSynchronousErrorHandling){let t=!Se;if(t&&(Se={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Se;if(Se=null,n)throw r}}else e()}function os(e){ee.useDeprecatedSynchronousErrorHandling&&Se&&(Se.errorThrown=!0,Se.error=e)}var Ae=class extends k{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,qt(t)&&t.add(this)):this.destination=tl}static create(t,n,r){return new tt(t,n,r)}next(t){this.isStopped?Nr(rs(t),this):this._next(t)}error(t){this.isStopped?Nr(ns(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Nr(ts,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Xu=Function.prototype.bind;function xr(e,t){return Xu.call(e,t)}var Sr=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Zt(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Zt(r)}else Zt(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Zt(n)}}},tt=class extends Ae{constructor(t,n,r){super();let o;if(y(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&ee.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&xr(t.next,i),error:t.error&&xr(t.error,i),complete:t.complete&&xr(t.complete,i)}):o=t}this.destination=new Sr(o)}};function Zt(e){ee.useDeprecatedSynchronousErrorHandling?os(e):Yt(e)}function el(e){throw e}function Nr(e,t){let{onStoppedNotification:n}=ee;n&&Xe.setTimeout(()=>n(e,t))}var tl={closed:!0,next:Ct,error:el,complete:Ct};var nt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function q(e){return e}function nl(...e){return Ar(e)}function Ar(e){return e.length===0?q:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var M=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=ol(n)?n:new tt(n,r,o);return et(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=is(r),new r((o,i)=>{let s=new tt({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[nt](){return this}pipe(...n){return Ar(n)(this)}toPromise(n){return n=is(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function is(e){var t;return(t=e??ee.Promise)!==null&&t!==void 0?t:Promise}function rl(e){return e&&y(e.next)&&y(e.error)&&y(e.complete)}function ol(e){return e&&e instanceof Ae||rl(e)&&qt(e)}function Or(e){return y(e?.lift)}function C(e){return t=>{if(Or(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function w(e,t,n,r,o){return new Rr(e,t,n,r,o)}var Rr=class extends Ae{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Pr(){return C((e,t)=>{let n=null;e._refCount++;let r=w(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Fr=class extends M{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Or(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new k;let n=this.getSubject();t.add(this.source.subscribe(w(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=k.EMPTY)}return t}refCount(){return Pr()(this)}};var ss=Ke(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ee=(()=>{class e extends M{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Qt(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new ss}next(n){et(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){et(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){et(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?_r:(this.currentObservers=null,i.push(n),new k(()=>{this.currentObservers=null,Et(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new M;return n.source=this,n}}return e.create=(t,n)=>new Qt(t,n),e})(),Qt=class extends Ee{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:_r}};var bt=class extends Ee{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Mt=new M(e=>e.complete());function as(e){return e&&y(e.schedule)}function cs(e){return e[e.length-1]}function Jt(e){return y(cs(e))?e.pop():void 0}function Ce(e){return as(cs(e))?e.pop():void 0}function ls(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,t||[])).next())})}function us(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Oe(e){return this instanceof Oe?(this.v=e,this):new Oe(e)}function ds(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(v,T){i.push([f,g,v,T])>1||c(f,g)})},h&&(o[f]=h(o[f])))}function c(f,h){try{u(r[f](h))}catch(g){p(i[0][3],g)}}function u(f){f.value instanceof Oe?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function p(f,h){f(h),i.shift(),i.length&&c(i[0][0],i[0][1])}}function fs(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof us=="function"?us(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var Kt=e=>e&&typeof e.length=="number"&&typeof e!="function";function Xt(e){return y(e?.then)}function en(e){return y(e[nt])}function tn(e){return Symbol.asyncIterator&&y(e?.[Symbol.asyncIterator])}function nn(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function il(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var rn=il();function on(e){return y(e?.[rn])}function sn(e){return ds(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Oe(n.read());if(o)return yield Oe(void 0);yield yield Oe(r)}}finally{n.releaseLock()}})}function an(e){return y(e?.getReader)}function F(e){if(e instanceof M)return e;if(e!=null){if(en(e))return sl(e);if(Kt(e))return al(e);if(Xt(e))return cl(e);if(tn(e))return ps(e);if(on(e))return ul(e);if(an(e))return ll(e)}throw nn(e)}function sl(e){return new M(t=>{let n=e[nt]();if(y(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function al(e){return new M(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function cl(e){return new M(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Yt)})}function ul(e){return new M(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function ps(e){return new M(t=>{dl(e,t).catch(n=>t.error(n))})}function ll(e){return ps(sn(e))}function dl(e,t){var n,r,o,i;return ls(this,void 0,void 0,function*(){try{for(n=fs(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function $(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function cn(e,t=0){return C((n,r)=>{n.subscribe(w(r,o=>$(r,e,()=>r.next(o),t),()=>$(r,e,()=>r.complete(),t),o=>$(r,e,()=>r.error(o),t)))})}function un(e,t=0){return C((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function hs(e,t){return F(e).pipe(un(t),cn(t))}function gs(e,t){return F(e).pipe(un(t),cn(t))}function ms(e,t){return new M(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function ys(e,t){return new M(n=>{let r;return $(n,t,()=>{r=e[rn](),$(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>y(r?.return)&&r.return()})}function ln(e,t){if(!e)throw new Error("Iterable cannot be null");return new M(n=>{$(n,t,()=>{let r=e[Symbol.asyncIterator]();$(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function vs(e,t){return ln(sn(e),t)}function Ds(e,t){if(e!=null){if(en(e))return hs(e,t);if(Kt(e))return ms(e,t);if(Xt(e))return gs(e,t);if(tn(e))return ln(e,t);if(on(e))return ys(e,t);if(an(e))return vs(e,t)}throw nn(e)}function fe(e,t){return t?Ds(e,t):F(e)}function dn(...e){let t=Ce(e);return fe(e,t)}function fl(e,t){let n=y(e)?e:()=>e,r=o=>o.error(n());return new M(t?o=>t.schedule(r,0,o):r)}function pl(e){return!!e&&(e instanceof M||y(e.lift)&&y(e.subscribe))}var Re=Ke(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function H(e,t){return C((n,r)=>{let o=0;n.subscribe(w(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:hl}=Array;function gl(e,t){return hl(t)?e(...t):e(t)}function fn(e){return H(t=>gl(e,t))}var{isArray:ml}=Array,{getPrototypeOf:yl,prototype:vl,keys:Dl}=Object;function pn(e){if(e.length===1){let t=e[0];if(ml(t))return{args:t,keys:null};if(wl(t)){let n=Dl(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function wl(e){return e&&typeof e=="object"&&yl(e)===vl}function hn(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Il(...e){let t=Ce(e),n=Jt(e),{args:r,keys:o}=pn(e);if(r.length===0)return fe([],t);let i=new M(El(r,t,o?s=>hn(o,s):q));return n?i.pipe(fn(n)):i}function El(e,t,n=q){return r=>{ws(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)ws(t,()=>{let u=fe(e[c],t),l=!1;u.subscribe(w(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function ws(e,t,n){e?$(n,e,t):t()}function Is(e,t,n,r,o,i,s,a){let c=[],u=0,l=0,d=!1,p=()=>{d&&!c.length&&!u&&t.complete()},f=g=>u<r?h(g):c.push(g),h=g=>{i&&t.next(g),u++;let v=!1;F(n(g,l++)).subscribe(w(t,T=>{o?.(T),i?f(T):t.next(T)},()=>{v=!0},void 0,()=>{if(v)try{for(u--;c.length&&u<r;){let T=c.shift();s?$(t,s,()=>h(T)):h(T)}p()}catch(T){t.error(T)}}))};return e.subscribe(w(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Pe(e,t,n=1/0){return y(t)?Pe((r,o)=>H((i,s)=>t(r,i,o,s))(F(e(r,o))),n):(typeof t=="number"&&(n=t),C((r,o)=>Is(r,o,e,n)))}function Es(e=1/0){return Pe(q,e)}function Cs(){return Es(1)}function gn(...e){return Cs()(fe(e,Ce(e)))}function Cl(e){return new M(t=>{F(e()).subscribe(t)})}function bl(...e){let t=Jt(e),{args:n,keys:r}=pn(e),o=new M(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;F(n[l]).subscribe(w(i,p=>{d||(d=!0,u--),a[l]=p},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?hn(r,a):a),i.complete())}))}});return t?o.pipe(fn(t)):o}function Fe(e,t){return C((n,r)=>{let o=0;n.subscribe(w(r,i=>e.call(t,i,o++)&&r.next(i)))})}function bs(e){return C((t,n)=>{let r=null,o=!1,i;r=t.subscribe(w(n,void 0,void 0,s=>{i=F(e(s,bs(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Ms(e,t,n,r,o){return(i,s)=>{let a=n,c=t,u=0;i.subscribe(w(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function kr(e,t){return y(t)?Pe(e,t,1):Pe(e,1)}function _t(e){return C((t,n)=>{let r=!1;t.subscribe(w(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Lr(e){return e<=0?()=>Mt:C((t,n)=>{let r=0;t.subscribe(w(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Ml(e){return H(()=>e)}function mn(e=_l){return C((t,n)=>{let r=!1;t.subscribe(w(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function _l(){return new Re}function jr(e){return C((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function _s(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Fe((o,i)=>e(o,i,r)):q,Lr(1),n?_t(t):mn(()=>new Re))}function Vr(e){return e<=0?()=>Mt:C((t,n)=>{let r=[];t.subscribe(w(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Tl(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Fe((o,i)=>e(o,i,r)):q,Vr(1),n?_t(t):mn(()=>new Re))}function xl(e,t){return C(Ms(e,t,arguments.length>=2,!0))}function Nl(...e){let t=Ce(e);return C((n,r)=>{(t?gn(e,n,t):gn(e,n)).subscribe(r)})}function Br(e,t){return C((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(w(r,c=>{o?.unsubscribe();let u=0,l=i++;F(e(c,l)).subscribe(o=w(r,d=>r.next(t?t(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Sl(e){return C((t,n)=>{F(e).subscribe(w(n,()=>n.complete(),Ct)),!n.closed&&t.subscribe(n)})}function $r(e,t,n){let r=y(e)||t||n?{next:e,error:t,complete:n}:e;return r?C((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(w(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):q}var la="https://g.co/ng/security#xss",_=class extends Error{constructor(t,n){super(Go(t,n)),this.code=t}};function Go(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function jt(e){return{toString:e}.toString()}var yn="__parameters__";function Ol(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function da(e,t,n){return jt(()=>{let r=Ol(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(yn)?c[yn]:Object.defineProperty(c,yn,{value:[]})[yn];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var at=globalThis;function R(e){for(let t in e)if(e[t]===R)return t;throw Error("Could not find renamed property on target object.")}function Rl(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function B(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(B).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Ts(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Pl=R({__forward_ref__:R});function fa(e){return e.__forward_ref__=fa,e.toString=function(){return B(this())},e}function V(e){return pa(e)?e():e}function pa(e){return typeof e=="function"&&e.hasOwnProperty(Pl)&&e.__forward_ref__===fa}function A(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function qo(e){return{providers:e.providers||[],imports:e.imports||[]}}function Zn(e){return xs(e,ha)||xs(e,ga)}function SE(e){return Zn(e)!==null}function xs(e,t){return e.hasOwnProperty(t)?e[t]:null}function Fl(e){let t=e&&(e[ha]||e[ga]);return t||null}function Ns(e){return e&&(e.hasOwnProperty(Ss)||e.hasOwnProperty(kl))?e[Ss]:null}var ha=R({\u0275prov:R}),Ss=R({\u0275inj:R}),ga=R({ngInjectableDef:R}),kl=R({ngInjectorDef:R}),x=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=A({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function ma(e){return e&&!!e.\u0275providers}var Ll=R({\u0275cmp:R}),jl=R({\u0275dir:R}),Vl=R({\u0275pipe:R}),Bl=R({\u0275mod:R}),_n=R({\u0275fac:R}),Tt=R({__NG_ELEMENT_ID__:R}),As=R({__NG_ENV_ID__:R});function lt(e){return typeof e=="string"?e:e==null?"":String(e)}function $l(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():lt(e)}function Hl(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new _(-200,e)}function Yo(e,t){throw new _(-201,!1)}var E=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(E||{}),ro;function ya(){return ro}function U(e){let t=ro;return ro=e,t}function va(e,t,n){let r=Zn(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&E.Optional)return null;if(t!==void 0)return t;Yo(e,"Injector")}var Ul={},xt=Ul,oo="__NG_DI_FLAG__",Tn="ngTempTokenPath",zl="ngTokenPath",Wl=/\n/gm,Gl="\u0275",Os="__source",ct;function ql(){return ct}function be(e){let t=ct;return ct=e,t}function Yl(e,t=E.Default){if(ct===void 0)throw new _(-203,!1);return ct===null?va(e,void 0,t):ct.get(e,t&E.Optional?null:void 0,t)}function N(e,t=E.Default){return(ya()||Yl)(V(e),t)}function O(e,t=E.Default){return N(e,Qn(t))}function Qn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function io(e){let t=[];for(let n=0;n<e.length;n++){let r=V(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=E.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Zl(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(N(o,i))}else t.push(N(r))}return t}function Da(e,t){return e[oo]=t,e.prototype[oo]=t,e}function Zl(e){return e[oo]}function Ql(e,t,n,r){let o=e[Tn];throw t[Os]&&o.unshift(t[Os]),e.message=Jl(`
`+e.message,o,n,r),e[zl]=o,e[Tn]=null,e}function Jl(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==Gl?e.slice(2):e;let o=B(t);if(Array.isArray(t))o=t.map(B).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):B(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Wl,`
  `)}`}var Kl=Da(da("Optional"),8);var Xl=Da(da("SkipSelf"),4);function je(e,t){let n=e.hasOwnProperty(_n);return n?e[_n]:null}function Zo(e,t){e.forEach(n=>Array.isArray(n)?Zo(n,t):t(n))}function wa(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function xn(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function ed(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function td(e,t,n){let r=Vt(e,t);return r>=0?e[r|1]=n:(r=~r,ed(e,r,t,n)),r}function Hr(e,t){let n=Vt(e,t);if(n>=0)return e[n|1]}function Vt(e,t){return nd(e,t,1)}function nd(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var dt={},Z=[],Nn=new x(""),Ia=new x("",-1),Ea=new x(""),Sn=class{get(t,n=xt){if(n===xt){let r=new Error(`NullInjectorError: No provider for ${B(t)}!`);throw r.name="NullInjectorError",r}return n}},Ca=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ca||{}),Nt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Nt||{}),Ve=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ve||{});function rd(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function so(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];od(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function ba(e){return e===3||e===4||e===6}function od(e){return e.charCodeAt(0)===64}function St(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Rs(e,n,o,null,t[++r]):Rs(e,n,o,null,null))}}return e}function Rs(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Ma="ng-template";function id(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&rd(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Qo(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Qo(e){return e.type===4&&e.value!==Ma}function sd(e,t,n){let r=e.type===4&&!n?Ma:e.value;return t===r}function ad(e,t,n){let r=4,o=e.attrs,i=o!==null?ld(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!te(r)&&!te(c))return!1;if(s&&te(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!sd(e,c,n)||c===""&&t.length===1){if(te(r))return!1;s=!0}}else if(r&8){if(o===null||!id(e,o,c,n)){if(te(r))return!1;s=!0}}else{let u=t[++a],l=cd(c,o,Qo(e),n);if(l===-1){if(te(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(te(r))return!1;s=!0}}}}return te(r)||s}function te(e){return(e&1)===0}function cd(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return dd(t,e)}function ud(e,t,n=!1){for(let r=0;r<t.length;r++)if(ad(e,t[r],n))return!0;return!1}function ld(e){for(let t=0;t<e.length;t++){let n=e[t];if(ba(n))return t}return e.length}function dd(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Ps(e,t){return e?":not("+t.trim()+")":t}function fd(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!te(s)&&(t+=Ps(i,o),o=""),r=s,i=i||!te(r);n++}return o!==""&&(t+=Ps(i,o)),t}function pd(e){return e.map(fd).join(",")}function hd(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!te(o))break;o=i}r++}return{attrs:t,classes:n}}function AE(e){return jt(()=>{let t=Na(e),n=It(Je({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ca.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Nt.Emulated,styles:e.styles||Z,_:null,schemas:e.schemas||null,tView:null,id:""});Sa(n);let r=e.dependencies;return n.directiveDefs=ks(r,!1),n.pipeDefs=ks(r,!0),n.id=vd(n),n})}function gd(e){return Be(e)||_a(e)}function md(e){return e!==null}function Jo(e){return jt(()=>({type:e.type,bootstrap:e.bootstrap||Z,declarations:e.declarations||Z,imports:e.imports||Z,exports:e.exports||Z,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Fs(e,t){if(e==null)return dt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ve.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ve.None?[r,a]:r,t[i]=s):n[i]=r}return n}function Ko(e){return jt(()=>{let t=Na(e);return Sa(t),t})}function Xo(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Be(e){return e[Ll]||null}function _a(e){return e[jl]||null}function Ta(e){return e[Vl]||null}function yd(e){let t=Be(e)||_a(e)||Ta(e);return t!==null?t.standalone:!1}function xa(e,t){let n=e[Bl]||null;if(!n&&t===!0)throw new Error(`Type ${B(e)} does not have '\u0275mod' property.`);return n}function Na(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||dt,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||Z,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Fs(e.inputs,t),outputs:Fs(e.outputs),debugInfo:null}}function Sa(e){e.features?.forEach(t=>t(e))}function ks(e,t){if(!e)return null;let n=t?Ta:gd;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(md)}function vd(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function ei(e){return{\u0275providers:e}}function Dd(...e){return{\u0275providers:Aa(!0,e),\u0275fromNgModule:!0}}function Aa(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Zo(t,s=>{let a=s;ao(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Oa(o,i),n}function Oa(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ti(o,i=>{t(i,r)})}}function ao(e,t,n,r){if(e=V(e),!e)return!1;let o=null,i=Ns(e),s=!i&&Be(e);if(!i&&!s){let c=e.ngModule;if(i=Ns(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)ao(u,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Zo(i.imports,l=>{ao(l,t,n,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Oa(u,t)}if(!a){let u=je(o)||(()=>new o);t({provide:o,useFactory:u,deps:Z},o),t({provide:Ea,useValue:o,multi:!0},o),t({provide:Nn,useValue:()=>N(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;ti(c,l=>{t(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function ti(e,t){for(let n of e)ma(n)&&(n=n.\u0275providers),Array.isArray(n)?ti(n,t):t(n)}var wd=R({provide:String,useValue:R});function Ra(e){return e!==null&&typeof e=="object"&&wd in e}function Id(e){return!!(e&&e.useExisting)}function Ed(e){return!!(e&&e.useFactory)}function ft(e){return typeof e=="function"}function Cd(e){return!!e.useClass}var Pa=new x(""),In={},bd={},Ur;function ni(){return Ur===void 0&&(Ur=new Sn),Ur}var ge=class{},At=class extends ge{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,uo(t,s=>this.processProvider(s)),this.records.set(Ia,rt(void 0,this)),o.has("environment")&&this.records.set(ge,rt(void 0,this));let i=this.records.get(Pa);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ea,Z,E.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=b(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),b(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=be(this),r=U(void 0),o;try{return t()}finally{be(n),U(r)}}get(t,n=xt,r=E.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(As))return t[As](this);r=Qn(r);let o,i=be(this),s=U(void 0);try{if(!(r&E.SkipSelf)){let c=this.records.get(t);if(c===void 0){let u=Nd(t)&&Zn(t);u&&this.injectableDefInScope(u)?c=rt(co(t),In):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&E.Self?ni():this.parent;return n=r&E.Optional&&n===xt?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Tn]=a[Tn]||[]).unshift(B(t)),i)throw a;return Ql(a,t,"R3InjectorError",this.source)}else throw a}finally{U(s),be(i)}}resolveInjectorInitializers(){let t=b(null),n=be(this),r=U(void 0),o;try{let i=this.get(Nn,Z,E.Self);for(let s of i)s()}finally{be(n),U(r),b(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(B(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new _(205,!1)}processProvider(t){t=V(t);let n=ft(t)?t:V(t&&t.provide),r=_d(t);if(!ft(t)&&t.multi===!0){let o=this.records.get(n);o||(o=rt(void 0,In,!0),o.factory=()=>io(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=b(null);try{return n.value===In&&(n.value=bd,n.value=n.factory()),typeof n.value=="object"&&n.value&&xd(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{b(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=V(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function co(e){let t=Zn(e),n=t!==null?t.factory:je(e);if(n!==null)return n;if(e instanceof x)throw new _(204,!1);if(e instanceof Function)return Md(e);throw new _(204,!1)}function Md(e){if(e.length>0)throw new _(204,!1);let n=Fl(e);return n!==null?()=>n.factory(e):()=>new e}function _d(e){if(Ra(e))return rt(void 0,e.useValue);{let t=Fa(e);return rt(t,In)}}function Fa(e,t,n){let r;if(ft(e)){let o=V(e);return je(o)||co(o)}else if(Ra(e))r=()=>V(e.useValue);else if(Ed(e))r=()=>e.useFactory(...io(e.deps||[]));else if(Id(e))r=()=>N(V(e.useExisting));else{let o=V(e&&(e.useClass||e.provide));if(Td(e))r=()=>new o(...io(e.deps));else return je(o)||co(o)}return r}function rt(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Td(e){return!!e.deps}function xd(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Nd(e){return typeof e=="function"||typeof e=="object"&&e instanceof x}function uo(e,t){for(let n of e)Array.isArray(n)?uo(n,t):n&&ma(n)?uo(n.\u0275providers,t):t(n)}function ka(e,t){e instanceof At&&e.assertNotDestroyed();let n,r=be(e),o=U(void 0);try{return t()}finally{be(r),U(o)}}function Sd(){return ya()!==void 0||ql()!=null}function Ad(e){return typeof e=="function"}var ve=0,I=1,m=2,j=3,ne=4,se=5,An=6,Ot=7,re=8,pt=9,oe=10,L=11,Rt=12,Ls=13,vt=14,me=15,Jn=16,ot=17,ht=18,Kn=19,La=20,Me=21,zr=22,$e=23,ie=25,ja=1;var He=7,On=8,Rn=9,Q=10,ri=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(ri||{});function ke(e){return Array.isArray(e)&&typeof e[ja]=="object"}function De(e){return Array.isArray(e)&&e[ja]===!0}function Va(e){return(e.flags&4)!==0}function Xn(e){return e.componentOffset>-1}function oi(e){return(e.flags&1)===1}function _e(e){return!!e.template}function Od(e){return(e[m]&512)!==0}var lo=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Ba(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function $a(){return Ha}function Ha(e){return e.type.prototype.ngOnChanges&&(e.setInput=Pd),Rd}$a.ngInherit=!0;function Rd(){let e=za(this),t=e?.current;if(t){let n=e.previous;if(n===dt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function Pd(e,t,n,r,o){let i=this.declaredInputs[r],s=za(e)||Fd(e,{previous:dt,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new lo(u&&u.currentValue,n,c===dt),Ba(e,t,o,n)}var Ua="__ngSimpleChanges__";function za(e){return e[Ua]||null}function Fd(e,t){return e[Ua]=t}var js=null;var pe=function(e,t,n){js?.(e,t,n)},Wa="svg",kd="math",Ld=!1;function jd(){return Ld}function ye(e){for(;Array.isArray(e);)e=e[ve];return e}function Ga(e,t){return ye(t[e])}function K(e,t){return ye(t[e.index])}function qa(e,t){return e.data[t]}function Ya(e,t){return e[t]}function Ne(e,t){let n=t[e];return ke(n)?n:n[ve]}function ii(e){return(e[m]&128)===128}function Vd(e){return De(e[j])}function Pn(e,t){return t==null?null:e[t]}function Za(e){e[ot]=0}function Bd(e){e[m]&1024||(e[m]|=1024,ii(e)&&Pt(e))}function $d(e,t){for(;e>0;)t=t[vt],e--;return t}function si(e){return!!(e[m]&9216||e[$e]?.dirty)}function fo(e){e[oe].changeDetectionScheduler?.notify(1),si(e)?Pt(e):e[m]&64&&(jd()?(e[m]|=1024,Pt(e)):e[oe].changeDetectionScheduler?.notify())}function Pt(e){e[oe].changeDetectionScheduler?.notify();let t=Ft(e);for(;t!==null&&!(t[m]&8192||(t[m]|=8192,!ii(t)));)t=Ft(t)}function Qa(e,t){if((e[m]&256)===256)throw new _(911,!1);e[Me]===null&&(e[Me]=[]),e[Me].push(t)}function Hd(e,t){if(e[Me]===null)return;let n=e[Me].indexOf(t);n!==-1&&e[Me].splice(n,1)}function Ft(e){let t=e[j];return De(t)?t[j]:t}var D={lFrame:ic(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Ud(){return D.lFrame.elementDepthCount}function zd(){D.lFrame.elementDepthCount++}function Wd(){D.lFrame.elementDepthCount--}function Ja(){return D.bindingsEnabled}function Gd(){return D.skipHydrationRootTNode!==null}function qd(e){return D.skipHydrationRootTNode===e}function Yd(){D.skipHydrationRootTNode=null}function P(){return D.lFrame.lView}function X(){return D.lFrame.tView}function OE(e){return D.lFrame.contextLView=e,e[re]}function RE(e){return D.lFrame.contextLView=null,e}function Y(){let e=Ka();for(;e!==null&&e.type===64;)e=e.parent;return e}function Ka(){return D.lFrame.currentTNode}function Zd(){let e=D.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Bt(e,t){let n=D.lFrame;n.currentTNode=e,n.isParent=t}function Xa(){return D.lFrame.isParent}function Qd(){D.lFrame.isParent=!1}function ec(){let e=D.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Jd(){return D.lFrame.bindingIndex}function Kd(e){return D.lFrame.bindingIndex=e}function ai(){return D.lFrame.bindingIndex++}function tc(e){let t=D.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Xd(){return D.lFrame.inI18n}function ef(e,t){let n=D.lFrame;n.bindingIndex=n.bindingRootIndex=e,po(t)}function tf(){return D.lFrame.currentDirectiveIndex}function po(e){D.lFrame.currentDirectiveIndex=e}function nf(e){let t=D.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function nc(e){D.lFrame.currentQueryIndex=e}function rf(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[se]:null}function rc(e,t,n){if(n&E.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&E.Host);)if(o=rf(i),o===null||(i=i[vt],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=D.lFrame=oc();return r.currentTNode=t,r.lView=e,!0}function ci(e){let t=oc(),n=e[I];D.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function oc(){let e=D.lFrame,t=e===null?null:e.child;return t===null?ic(e):t}function ic(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function sc(){let e=D.lFrame;return D.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var ac=sc;function ui(){let e=sc();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function of(e){return(D.lFrame.contextLView=$d(e,D.lFrame.contextLView))[re]}function Ye(){return D.lFrame.selectedIndex}function Ue(e){D.lFrame.selectedIndex=e}function cc(){let e=D.lFrame;return qa(e.tView,e.selectedIndex)}function PE(){D.lFrame.currentNamespace=Wa}function FE(){sf()}function sf(){D.lFrame.currentNamespace=null}function af(){return D.lFrame.currentNamespace}var uc=!0;function li(){return uc}function di(e){uc=e}function cf(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ha(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function fi(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),u&&((e.viewHooks??=[]).push(n,u),(e.viewCheckHooks??=[]).push(n,u)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function En(e,t,n){lc(e,t,3,n)}function Cn(e,t,n,r){(e[m]&3)===n&&lc(e,t,n,r)}function Wr(e,t){let n=e[m];(n&3)===t&&(n&=16383,n+=1,e[m]=n)}function lc(e,t,n,r){let o=r!==void 0?e[ot]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[ot]+=65536),(a<i||i==-1)&&(uf(e,n,t,c),e[ot]=(e[ot]&**********)+c+2),c++}function Vs(e,t){pe(4,e,t);let n=b(null);try{t.call(e)}finally{b(n),pe(5,e,t)}}function uf(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[m]>>14<e[ot]>>16&&(e[m]&3)===t&&(e[m]+=16384,Vs(a,i)):Vs(a,i)}var ut=-1,ze=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function lf(e){return e instanceof ze}function df(e){return(e.flags&8)!==0}function ff(e){return(e.flags&16)!==0}function dc(e){return e!==ut}function Fn(e){return e&32767}function pf(e){return e>>16}function kn(e,t){let n=pf(e),r=t;for(;n>0;)r=r[vt],n--;return r}var ho=!0;function Ln(e){let t=ho;return ho=e,t}var hf=256,fc=hf-1,pc=5,gf=0,he={};function mf(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Tt)&&(r=n[Tt]),r==null&&(r=n[Tt]=gf++);let o=r&fc,i=1<<o;t.data[e+(o>>pc)]|=i}function jn(e,t){let n=hc(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,Gr(r.data,e),Gr(t,null),Gr(r.blueprint,null));let o=pi(e,t),i=e.injectorIndex;if(dc(o)){let s=Fn(o),a=kn(o,t),c=a[I].data;for(let u=0;u<8;u++)t[i+u]=a[s+u]|c[s+u]}return t[i+8]=o,i}function Gr(e,t){e.push(0,0,0,0,0,0,0,0,t)}function hc(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function pi(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Dc(o),r===null)return ut;if(n++,o=o[vt],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return ut}function go(e,t,n){mf(e,t,n)}function yf(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(ba(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function gc(e,t,n){if(n&E.Optional||e!==void 0)return e;Yo(t,"NodeInjector")}function mc(e,t,n,r){if(n&E.Optional&&r===void 0&&(r=null),!(n&(E.Self|E.Host))){let o=e[pt],i=U(void 0);try{return o?o.get(t,r,n&E.Optional):va(t,r,n&E.Optional)}finally{U(i)}}return gc(r,t,n)}function yc(e,t,n,r=E.Default,o){if(e!==null){if(t[m]&2048&&!(r&E.Self)){let s=Ef(e,t,n,r,he);if(s!==he)return s}let i=vc(e,t,n,r,he);if(i!==he)return i}return mc(t,n,r,o)}function vc(e,t,n,r,o){let i=wf(n);if(typeof i=="function"){if(!rc(t,e,r))return r&E.Host?gc(o,n,r):mc(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&E.Optional))Yo(n);else return s}finally{ac()}}else if(typeof i=="number"){let s=null,a=hc(e,t),c=ut,u=r&E.Host?t[me][se]:null;for((a===-1||r&E.SkipSelf)&&(c=a===-1?pi(e,t):t[a+8],c===ut||!$s(r,!1)?a=-1:(s=t[I],a=Fn(c),t=kn(c,t)));a!==-1;){let l=t[I];if(Bs(i,a,l.data)){let d=vf(a,t,n,s,r,u);if(d!==he)return d}c=t[a+8],c!==ut&&$s(r,t[I].data[a+8]===u)&&Bs(i,a,t)?(s=l,a=Fn(c),t=kn(c,t)):a=-1}}return o}function vf(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],c=r==null?Xn(a)&&ho:r!=s&&(a.type&3)!==0,u=o&E.Host&&i===a,l=Df(a,s,n,c,u);return l!==null?gt(t,s,l,a):he}function Df(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:u;for(let f=d;f<p;f++){let h=s[f];if(f<c&&n===h||f>=c&&h.type===n)return f}if(o){let f=s[c];if(f&&_e(f)&&f.type===n)return c}return null}function gt(e,t,n,r){let o=e[n],i=t.data;if(lf(o)){let s=o;s.resolving&&Hl($l(i[n]));let a=Ln(s.canSeeViewProviders);s.resolving=!0;let c,u=s.injectImpl?U(s.injectImpl):null,l=rc(e,r,E.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&cf(n,i[n],t)}finally{u!==null&&U(u),Ln(a),s.resolving=!1,ac()}}return o}function wf(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Tt)?e[Tt]:void 0;return typeof t=="number"?t>=0?t&fc:If:t}function Bs(e,t,n){let r=1<<e;return!!(n[t+(e>>pc)]&r)}function $s(e,t){return!(e&E.Self)&&!(e&E.Host&&t)}var Le=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return yc(this._tNode,this._lView,t,Qn(r),n)}};function If(){return new Le(Y(),P())}function kE(e){return jt(()=>{let t=e.prototype.constructor,n=t[_n]||mo(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[_n]||mo(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function mo(e){return pa(e)?()=>{let t=mo(V(e));return t&&t()}:je(e)}function Ef(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[m]&2048&&!(s[m]&512);){let a=vc(i,s,n,r|E.Self,he);if(a!==he)return a;let c=i.parent;if(!c){let u=s[La];if(u){let l=u.get(n,he,r);if(l!==he)return l}c=Dc(s),s=s[vt]}i=c}return o}function Dc(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[se]:null}function Cf(e){return yf(Y(),e)}function Hs(e,t=null,n=null,r){let o=wc(e,t,n,r);return o.resolveInjectorInitializers(),o}function wc(e,t=null,n=null,r,o=new Set){let i=[n||Z,Dd(e)];return r=r||(typeof e=="object"?void 0:B(e)),new At(i,t||ni(),r||null,o)}var hi=(()=>{class e{static{this.THROW_IF_NOT_FOUND=xt}static{this.NULL=new Sn}static create(n,r){if(Array.isArray(n))return Hs({name:""},r,n,"");{let o=n.name??"";return Hs({name:o},n.parent,n.providers,o)}}static{this.\u0275prov=A({token:e,providedIn:"any",factory:()=>N(Ia)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();var bf="ngOriginalError";function qr(e){return e[bf]}var mt=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&qr(t);for(;n&&qr(n);)n=qr(n);return n||null}},Ic=new x("",{providedIn:"root",factory:()=>O(mt).handleError.bind(void 0)}),Ec=(()=>{class e{static{this.__NG_ELEMENT_ID__=Mf}static{this.__NG_ENV_ID__=n=>n}}return e})(),yo=class extends Ec{constructor(t){super(),this._lView=t}onDestroy(t){return Qa(this._lView,t),()=>Hd(this._lView,t)}};function Mf(){return new yo(P())}function _f(){return er(Y(),P())}function er(e,t){return new gi(K(e,t))}var gi=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=_f}}return e})();var vo=class extends Ee{constructor(t=!1){super(),this.destroyRef=void 0,this.__isAsync=t,Sd()&&(this.destroyRef=O(Ec,{optional:!0})??void 0)}emit(t){let n=b(null);try{super.next(t)}finally{b(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=Yr(i),o&&(o=Yr(o)),s&&(s=Yr(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof k&&t.add(a),a}};function Yr(e){return t=>{setTimeout(e,void 0,t)}}var it=vo;function Cc(e){return(e.flags&128)===128}var bc=new Map,Tf=0;function xf(){return Tf++}function Nf(e){bc.set(e[Kn],e)}function Sf(e){bc.delete(e[Kn])}var Us="__ngContext__";function We(e,t){ke(t)?(e[Us]=t[Kn],Nf(t)):e[Us]=t}function Mc(e){return Tc(e[Rt])}function _c(e){return Tc(e[ne])}function Tc(e){for(;e!==null&&!De(e);)e=e[ne];return e}var Do;function LE(e){Do=e}function Af(){if(Do!==void 0)return Do;if(typeof document<"u")return document;throw new _(210,!1)}var jE=new x("",{providedIn:"root",factory:()=>Of}),Of="ng",Rf=new x(""),mi=new x("",{providedIn:"platform",factory:()=>"unknown"});var VE=new x("",{providedIn:"root",factory:()=>Af().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Pf="h",Ff="b";var kf=()=>null;function yi(e,t,n=!1){return kf(e,t,n)}var xc=!1,Lf=new x("",{providedIn:"root",factory:()=>xc});var vn;function jf(){if(vn===void 0&&(vn=null,at.trustedTypes))try{vn=at.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return vn}function zs(e){return jf()?.createScriptURL(e)||e}var Vn=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${la})`}};function tr(e){return e instanceof Vn?e.changingThisBreaksApplicationSecurity:e}function Nc(e,t){let n=Vf(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${la})`)}return n===t}function Vf(e){return e instanceof Vn&&e.getTypeName()||null}var Bf=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function $f(e){return e=String(e),e.match(Bf)?e:"unsafe:"+e}var vi=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(vi||{});function Hf(e){let t=Sc();return t?t.sanitize(vi.URL,e)||"":Nc(e,"URL")?tr(e):$f(lt(e))}function Uf(e){let t=Sc();if(t)return zs(t.sanitize(vi.RESOURCE_URL,e)||"");if(Nc(e,"ResourceURL"))return zs(tr(e));throw new _(904,!1)}function zf(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Uf:Hf}function BE(e,t,n){return zf(t,n)(e)}function Sc(){let e=P();return e&&e[oe].sanitizer}function Ac(e){return e instanceof Function?e():e}var Bn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Bn||{}),Wf;function Di(e,t){return Wf(e,t)}function st(e,t,n,r,o){if(r!=null){let i,s=!1;De(r)?i=r:ke(r)&&(s=!0,r=r[ve]);let a=ye(r);e===0&&n!==null?o==null?kc(t,n,a):$n(t,n,a,o||null,!0):e===1&&n!==null?$n(t,n,a,o||null,!0):e===2?cp(t,a,s):e===3&&t.destroyNode(a),i!=null&&lp(t,e,i,n,o)}}function Gf(e,t){return e.createText(t)}function qf(e,t,n){e.setValue(t,n)}function Oc(e,t,n){return e.createElement(t,n)}function Yf(e,t){Rc(e,t),t[ve]=null,t[se]=null}function Zf(e,t,n,r,o,i){r[ve]=o,r[se]=t,nr(e,r,n,1,o,i)}function Rc(e,t){t[oe].changeDetectionScheduler?.notify(1),nr(e,t,t[L],2,null,null)}function Qf(e){let t=e[Rt];if(!t)return Zr(e[I],e);for(;t;){let n=null;if(ke(t))n=t[Rt];else{let r=t[Q];r&&(n=r)}if(!n){for(;t&&!t[ne]&&t!==e;)ke(t)&&Zr(t[I],t),t=t[j];t===null&&(t=e),ke(t)&&Zr(t[I],t),n=t&&t[ne]}t=n}}function Jf(e,t,n,r){let o=Q+r,i=n.length;r>0&&(n[o-1][ne]=t),r<i-Q?(t[ne]=n[o],wa(n,Q+r,t)):(n.push(t),t[ne]=null),t[j]=n;let s=t[Jn];s!==null&&n!==s&&Kf(s,t);let a=t[ht];a!==null&&a.insertView(e),fo(t),t[m]|=128}function Kf(e,t){let n=e[Rn],o=t[j][j][me];t[me]!==o&&(e[m]|=ri.HasTransplantedViews),n===null?e[Rn]=[t]:n.push(t)}function Pc(e,t){let n=e[Rn],r=n.indexOf(t);n.splice(r,1)}function wo(e,t){if(e.length<=Q)return;let n=Q+t,r=e[n];if(r){let o=r[Jn];o!==null&&o!==e&&Pc(o,r),t>0&&(e[n-1][ne]=r[ne]);let i=xn(e,Q+t);Yf(r[I],r);let s=i[ht];s!==null&&s.detachView(i[I]),r[j]=null,r[ne]=null,r[m]&=-129}return r}function Fc(e,t){if(!(t[m]&256)){let n=t[L];n.destroyNode&&nr(e,t,n,3,null,null),Qf(t)}}function Zr(e,t){if(t[m]&256)return;let n=b(null);try{t[m]&=-129,t[m]|=256,t[$e]&&Ki(t[$e]),ep(e,t),Xf(e,t),t[I].type===1&&t[L].destroy();let r=t[Jn];if(r!==null&&De(t[j])){r!==t[j]&&Pc(r,t);let o=t[ht];o!==null&&o.detachView(e)}Sf(t)}finally{b(n)}}function Xf(e,t){let n=e.cleanup,r=t[Ot];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[Ot]=null);let o=t[Me];if(o!==null){t[Me]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function ep(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof ze)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];pe(4,a,c);try{c.call(a)}finally{pe(5,a,c)}}else{pe(4,o,i);try{i.call(o)}finally{pe(5,o,i)}}}}}function tp(e,t,n){return np(e,t.parent,n)}function np(e,t,n){let r=t;for(;r!==null&&r.type&40;)t=r,r=t.parent;if(r===null)return n[ve];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===Nt.None||i===Nt.Emulated)return null}return K(r,n)}}function $n(e,t,n,r,o){e.insertBefore(t,n,r,o)}function kc(e,t,n){e.appendChild(t,n)}function Ws(e,t,n,r,o){r!==null?$n(e,t,n,r,o):kc(e,t,n)}function rp(e,t,n,r){e.removeChild(t,n,r)}function wi(e,t){return e.parentNode(t)}function op(e,t){return e.nextSibling(t)}function ip(e,t,n){return ap(e,t,n)}function sp(e,t,n){return e.type&40?K(e,n):null}var ap=sp,Gs;function Ii(e,t,n,r){let o=tp(e,r,t),i=t[L],s=r.parent||t[se],a=ip(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Ws(i,o,n[c],a,!1);else Ws(i,o,n,a,!1);Gs!==void 0&&Gs(i,r,t,n,o)}function bn(e,t){if(t!==null){let n=t.type;if(n&3)return K(t,e);if(n&4)return Io(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return bn(e,r);{let o=e[t.index];return De(o)?Io(-1,o):ye(o)}}else{if(n&32)return Di(t,e)()||ye(e[t.index]);{let r=Lc(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Ft(e[me]);return bn(o,r)}else return bn(e,t.next)}}}return null}function Lc(e,t){if(t!==null){let r=e[me][se],o=t.projection;return r.projection[o]}return null}function Io(e,t){let n=Q+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return bn(r,o)}return t[He]}function cp(e,t,n){let r=wi(e,t);r&&rp(e,r,t,n)}function Ei(e,t,n,r,o,i,s){for(;n!=null;){let a=r[n.index],c=n.type;if(s&&t===0&&(a&&We(ye(a),r),n.flags|=2),(n.flags&32)!==32)if(c&8)Ei(e,t,n.child,r,o,i,!1),st(t,e,o,a,i);else if(c&32){let u=Di(n,r),l;for(;l=u();)st(t,e,o,l,i);st(t,e,o,a,i)}else c&16?up(e,t,r,n,o,i):st(t,e,o,a,i);n=s?n.projectionNext:n.next}}function nr(e,t,n,r,o,i){Ei(n,r,e.firstChild,t,o,i,!1)}function up(e,t,n,r,o,i){let s=n[me],c=s[se].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];st(t,e,o,l,i)}else{let u=c,l=s[j];Cc(r)&&(u.flags|=128),Ei(e,t,u,l,o,i,!0)}}function lp(e,t,n,r,o){let i=n[He],s=ye(n);i!==s&&st(t,e,r,i,o);for(let a=Q;a<n.length;a++){let c=n[a];nr(c[I],c,e,t,r,i)}}function dp(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Bn.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Bn.Important),e.setStyle(n,r,o,i))}}function fp(e,t,n){e.setAttribute(t,"style",n)}function jc(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Vc(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&so(e,t,r),o!==null&&jc(e,t,o),i!==null&&fp(e,t,i)}var we={};function $E(e=1){Bc(X(),P(),Ye()+e,!1)}function Bc(e,t,n,r){if(!r)if((t[m]&3)===3){let i=e.preOrderCheckHooks;i!==null&&En(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Cn(t,i,0,n)}Ue(n)}function ae(e,t=E.Default){let n=P();if(n===null)return N(e,t);let r=Y();return yc(r,n,V(e),t)}function $c(e,t,n,r,o,i){let s=b(null);try{let a=null;o&Ve.SignalBased&&(a=t[r][Yi]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ve.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Ba(t,a,r,i)}finally{b(s)}}function pp(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Ue(~o);else{let i=o,s=n[++r],a=n[++r];ef(s,i);let c=t[i];a(2,c)}}}finally{Ue(-1)}}function rr(e,t,n,r,o,i,s,a,c,u,l){let d=t.blueprint.slice();return d[ve]=o,d[m]=r|4|128|8|64,(u!==null||e&&e[m]&2048)&&(d[m]|=2048),Za(d),d[j]=d[vt]=e,d[re]=n,d[oe]=s||e&&e[oe],d[L]=a||e&&e[L],d[pt]=c||e&&e[pt]||null,d[se]=i,d[Kn]=xf(),d[An]=l,d[La]=u,d[me]=t.type==2?e[me]:d,d}function or(e,t,n,r,o){let i=e.data[t];if(i===null)i=hp(e,t,n,r,o),Xd()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Zd();i.injectorIndex=s===null?-1:s.injectorIndex}return Bt(i,!0),i}function hp(e,t,n,r,o){let i=Ka(),s=Xa(),a=s?i:i&&i.parent,c=e.data[t]=Dp(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=c),i!==null&&(s?i.child==null&&c.parent!==null&&(i.child=c):i.next===null&&(i.next=c,c.prev=i)),c}function Hc(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Uc(e,t,n,r,o){let i=Ye(),s=r&2;try{Ue(-1),s&&t.length>ie&&Bc(e,t,ie,!1),pe(s?2:0,o),n(r,o)}finally{Ue(i),pe(s?3:1,o)}}function zc(e,t,n){if(Va(t)){let r=b(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{b(r)}}}function Wc(e,t,n){Ja()&&(_p(e,t,n,K(n,t)),(n.flags&64)===64&&Qc(e,t,n))}function Gc(e,t,n=K){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function qc(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Ci(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ci(e,t,n,r,o,i,s,a,c,u,l){let d=ie+r,p=d+o,f=gp(d,p),h=typeof u=="function"?u():u;return f[I]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:h,incompleteFirstPass:!1,ssrId:l}}function gp(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:we);return n}function mp(e,t,n,r){let i=r.get(Lf,xc)||n===Nt.ShadowDom,s=e.selectRootElement(t,i);return yp(s),s}function yp(e){vp(e)}var vp=()=>null;function Dp(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Gd()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function qs(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,c=Ve.None;Array.isArray(s)?(a=s[0],c=s[1]):a=s;let u=i;if(o!==null){if(!o.hasOwnProperty(i))continue;u=o[i]}e===0?Ys(r,n,u,a,c):Ys(r,n,u,a)}return r}function Ys(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function wp(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],c=null,u=null;for(let l=r;l<o;l++){let d=i[l],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;c=qs(0,d.inputs,l,c,f),u=qs(1,d.outputs,l,u,h);let g=c!==null&&s!==null&&!Qo(t)?Lp(c,l,s):null;a.push(g)}c!==null&&(c.hasOwnProperty("class")&&(t.flags|=8),c.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=c,t.outputs=u}function Ip(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Ep(e,t,n,r,o,i,s,a){let c=K(t,n),u=t.inputs,l;!a&&u!=null&&(l=u[r])?(bi(e,n,l,r,o),Xn(t)&&Cp(n,t.index)):t.type&3?(r=Ip(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)):t.type&12}function Cp(e,t){let n=Ne(t,e);n[m]&16||(n[m]|=64)}function Yc(e,t,n,r){if(Ja()){let o=r===null?null:{"":-1},i=xp(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&Zc(e,t,n,s,o,a),o&&Np(n,r,o)}n.mergedAttrs=St(n.mergedAttrs,n.attrs)}function Zc(e,t,n,r,o,i){for(let u=0;u<r.length;u++)go(jn(n,t),e,r[u].type);Ap(n,e.data.length,r.length);for(let u=0;u<r.length;u++){let l=r[u];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,c=Hc(e,t,r.length,null);for(let u=0;u<r.length;u++){let l=r[u];n.mergedAttrs=St(n.mergedAttrs,l.hostAttrs),Op(e,n,t,c,l),Sp(c,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),c++}wp(e,n,i)}function bp(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;Mp(s)!=a&&s.push(a),s.push(n,r,i)}}function Mp(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function _p(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;Xn(n)&&Rp(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||jn(n,t),We(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let c=e.data[a],u=gt(t,e,a,n);if(We(u,t),s!==null&&kp(t,a-o,u,c,n,s),_e(c)){let l=Ne(n.index,t);l[re]=gt(t,e,a,n)}}}function Qc(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=tf();try{Ue(i);for(let a=r;a<o;a++){let c=e.data[a],u=t[a];po(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Tp(c,u)}}finally{Ue(-1),po(s)}}function Tp(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function xp(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(ud(t,s.selectors,!1))if(r||(r=[]),_e(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let c=a.length;Eo(e,t,c)}else r.unshift(s),Eo(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Eo(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Np(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}}function Sp(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;_e(t)&&(n[""]=e)}}function Ap(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Op(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=je(o.type,!0)),s=new ze(i,_e(o),ae);e.blueprint[r]=s,n[r]=s,bp(e,t,r,Hc(e,n,o.hostVars,we),o)}function Rp(e,t,n){let r=K(t,e),o=qc(n),i=e[oe].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=ir(e,rr(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function Pp(e,t,n,r,o,i){let s=K(e,t);Fp(t[L],s,i,e.value,n,r,o)}function Fp(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?lt(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function kp(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let c=s[a++],u=s[a++],l=s[a++],d=s[a++];$c(r,n,c,u,l,d)}}function Lp(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function Jc(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Kc(e,t){let n=e.contentQueries;if(n!==null){let r=b(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];nc(i),a.contentQueries(2,t[s],s)}}}finally{b(r)}}}function ir(e,t){return e[Rt]?e[Ls][ne]=t:e[Rt]=t,e[Ls]=t,t}function Co(e,t,n){nc(0);let r=b(null);try{t(e,n)}finally{b(r)}}function jp(e){return e[Ot]||(e[Ot]=[])}function Vp(e){return e.cleanup||(e.cleanup=[])}function Xc(e,t){let n=e[pt],r=n?n.get(mt,null):null;r&&r.handleError(t)}function bi(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],c=n[i++],u=t[s],l=e.data[s];$c(l,u,r,a,c,o)}}function eu(e,t,n){let r=Ga(t,e);qf(e[L],r,n)}function Bp(e,t){let n=Ne(t,e),r=n[I];$p(r,n);let o=n[ve];o!==null&&n[An]===null&&(n[An]=yi(o,n[pt])),Mi(r,n,n[re])}function $p(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Mi(e,t,n){ci(t);try{let r=e.viewQuery;r!==null&&Co(1,r,n);let o=e.template;o!==null&&Uc(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[ht]?.finishViewCreation(e),e.staticContentQueries&&Kc(e,t),e.staticViewQueries&&Co(2,e.viewQuery,n);let i=e.components;i!==null&&Hp(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[m]&=-5,ui()}}function Hp(e,t){for(let n=0;n<t.length;n++)Bp(e,t[n])}function Up(e,t,n,r){let o=b(null);try{let i=t.tView,a=e[m]&4096?4096:16,c=rr(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[t.index];c[Jn]=u;let l=e[ht];return l!==null&&(c[ht]=l.createEmbeddedView(i)),Mi(i,c,n),c}finally{b(o)}}function Zs(e,t){return!t||t.firstChild===null||Cc(e)}function zp(e,t,n,r=!0){let o=t[I];if(Jf(o,t,e,n),r){let s=Io(n,e),a=t[L],c=wi(a,e[He]);c!==null&&Zf(o,e[se],a,t,c,s)}let i=t[An];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Hn(e,t,n,r,o=!1){for(;n!==null;){let i=t[n.index];i!==null&&r.push(ye(i)),De(i)&&Wp(i,r);let s=n.type;if(s&8)Hn(e,t,n.child,r);else if(s&32){let a=Di(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Lc(t,n);if(Array.isArray(a))r.push(...a);else{let c=Ft(t[me]);Hn(c[I],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Wp(e,t){for(let n=Q;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&Hn(r[I],r,o,t)}e[He]!==e[ve]&&t.push(e[He])}var tu=[];function Gp(e){return e[$e]??qp(e)}function qp(e){let t=tu.pop()??Object.create(Zp);return t.lView=e,t}function Yp(e){e.lView[$e]!==e&&(e.lView=null,tu.push(e))}var Zp=It(Je({},Zi),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Pt(e.lView)},consumerOnSignalRead(){this.lView[$e]=this}}),nu=100;function ru(e,t=!0,n=0){let r=e[oe],o=r.rendererFactory,i=!1;i||o.begin?.();try{Qp(e,n)}catch(s){throw t&&Xc(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function Qp(e,t){bo(e,t);let n=0;for(;si(e);){if(n===nu)throw new _(103,!1);n++,bo(e,1)}}function Jp(e,t,n,r){let o=t[m];if((o&256)===256)return;let i=!1;!i&&t[oe].inlineEffectRunner?.flush(),ci(t);let s=null,a=null;!i&&Kp(e)&&(a=Gp(t),s=Qi(a));try{Za(t),Kd(e.bindingStartIndex),n!==null&&Uc(e,t,n,2,r);let c=(o&3)===3;if(!i)if(c){let d=e.preOrderCheckHooks;d!==null&&En(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Cn(t,d,0,null),Wr(t,0)}if(Xp(t),ou(t,0),e.contentQueries!==null&&Kc(e,t),!i)if(c){let d=e.contentCheckHooks;d!==null&&En(t,d)}else{let d=e.contentHooks;d!==null&&Cn(t,d,1),Wr(t,1)}pp(e,t);let u=e.components;u!==null&&su(t,u,0);let l=e.viewQuery;if(l!==null&&Co(2,l,r),!i)if(c){let d=e.viewCheckHooks;d!==null&&En(t,d)}else{let d=e.viewHooks;d!==null&&Cn(t,d,2),Wr(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[zr]){for(let d of t[zr])d();t[zr]=null}i||(t[m]&=-73)}catch(c){throw Pt(t),c}finally{a!==null&&(Ji(a,s),Yp(a)),ui()}}function Kp(e){return e.type!==2}function ou(e,t){for(let n=Mc(e);n!==null;n=_c(n))for(let r=Q;r<n.length;r++){let o=n[r];iu(o,t)}}function Xp(e){for(let t=Mc(e);t!==null;t=_c(t)){if(!(t[m]&ri.HasTransplantedViews))continue;let n=t[Rn];for(let r=0;r<n.length;r++){let o=n[r],i=o[j];Bd(o)}}}function eh(e,t,n){let r=Ne(t,e);iu(r,n)}function iu(e,t){ii(e)&&bo(e,t)}function bo(e,t){let r=e[I],o=e[m],i=e[$e],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Cr(i)),i&&(i.dirty=!1),e[m]&=-9217,s)Jp(r,e,r.template,e[re]);else if(o&8192){ou(e,1);let a=r.components;a!==null&&su(e,a,1)}}function su(e,t,n){for(let r=0;r<t.length;r++)eh(e,t[r],n)}function _i(e){for(e[oe].changeDetectionScheduler?.notify();e;){e[m]|=64;let t=Ft(e);if(Od(e)&&!t)return e;e=t}return null}var Ge=class{get rootNodes(){let t=this._lView,n=t[I];return Hn(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[re]}set context(t){this._lView[re]=t}get destroyed(){return(this._lView[m]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[j];if(De(t)){let n=t[On],r=n?n.indexOf(this):-1;r>-1&&(wo(t,r),xn(n,r))}this._attachedToViewContainer=!1}Fc(this._lView[I],this._lView)}onDestroy(t){Qa(this._lView,t)}markForCheck(){_i(this._cdRefInjectingView||this._lView)}detach(){this._lView[m]&=-129}reattach(){fo(this._lView),this._lView[m]|=128}detectChanges(){this._lView[m]|=1024,ru(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,Rc(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t,fo(this._lView)}},sr=(()=>{class e{static{this.__NG_ELEMENT_ID__=rh}}return e})(),th=sr,nh=class extends th{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Up(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Ge(o)}};function rh(){return oh(Y(),P())}function oh(e,t){return e.type&4?new nh(t,e,er(e,t)):null}var UE=new RegExp(`^(\\d+)*(${Ff}|${Pf})*(.*)`);var ih=()=>null;function Qs(e,t){return ih(e,t)}var Mo=class{},_o=class{},Un=class{};function sh(e){let t=Error(`No component factory found for ${B(e)}.`);return t[ah]=e,t}var ah="ngComponent";var To=class{resolveComponentFactory(t){throw sh(t)}},ar=(()=>{class e{static{this.NULL=new To}}return e})(),xo=class{},au=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>ch()}}return e})();function ch(){let e=P(),t=Y(),n=Ne(t.index,e);return(ke(n)?n:e)[L]}var uh=(()=>{class e{static{this.\u0275prov=A({token:e,providedIn:"root",factory:()=>null})}}return e})(),Qr={};var Js=new Set;function Ti(e){Js.has(e)||(Js.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function Ks(...e){}function lh(){let e=typeof at.requestAnimationFrame=="function",t=at[e?"requestAnimationFrame":"setTimeout"],n=at[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){let r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);let o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}var J=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new it(!1),this.onMicrotaskEmpty=new it(!1),this.onStable=new it(!1),this.onError=new it(!1),typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=lh().nativeRequestAnimationFrame,ph(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,dh,Ks,Ks);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},dh={};function xi(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function fh(e){e.isCheckStableRunning||e.lastRequestAnimationFrameId!==-1||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(at,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,No(e),e.isCheckStableRunning=!0,xi(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),No(e))}function ph(e){let t=()=>{fh(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(hh(a))return n.invokeTask(o,i,s,a);try{return Xs(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),ea(e)}},onInvoke:(n,r,o,i,s,a,c)=>{try{return Xs(e),n.invoke(o,i,s,a,c)}finally{e.shouldCoalesceRunChangeDetection&&t(),ea(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,No(e),xi(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function No(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.lastRequestAnimationFrameId!==-1?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Xs(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function ea(e){e._nesting--,xi(e)}function hh(e){return!Array.isArray(e)||e.length!==1?!1:e[0].data?.__ignore_ng_zone__===!0}var cu=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=A({token:e,providedIn:"root",factory:()=>new e})}}return e})();function So(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ts(o,a);else if(i==2){let c=a,u=t[++s];r=Ts(r,c+": "+u+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var zn=class extends ar{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Be(t);return new kt(n,this.ngModule)}};function ta(e){let t=[];for(let n in e){if(!e.hasOwnProperty(n))continue;let r=e[n];r!==void 0&&t.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return t}function gh(e){let t=e.toLowerCase();return t==="svg"?Wa:t==="math"?kd:null}var Ao=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Qn(r);let o=this.injector.get(t,Qr,r);return o!==Qr||n===Qr?o:this.parentInjector.get(t,n,r)}},kt=class extends Un{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=ta(t.inputs);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return ta(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=pd(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=b(null);try{o=o||this.ngModule;let s=o instanceof ge?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Ao(t,s):t,c=a.get(xo,null);if(c===null)throw new _(407,!1);let u=a.get(uh,null),l=a.get(cu,null),d=a.get(Mo,null),p={rendererFactory:c,sanitizer:u,inlineEffectRunner:null,afterRenderEventManager:l,changeDetectionScheduler:d},f=c.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",g=r?mp(f,r,this.componentDef.encapsulation,a):Oc(f,h,gh(h)),v=512;this.componentDef.signals?v|=4096:this.componentDef.onPush||(v|=16);let T=null;g!==null&&(T=yi(g,a,!0));let de=Ci(0,null,null,1,0,null,null,null,null,null,null),S=rr(null,de,null,v,null,null,p,f,a,null,T);ci(S);let W,Ie;try{let G=this.componentDef,Qe,Ir=null;G.findHostDirectiveDefs?(Qe=[],Ir=new Map,G.findHostDirectiveDefs(G,Qe,Ir),Qe.push(G)):Qe=[G];let Hu=mh(S,g),Uu=yh(Hu,g,G,Qe,S,p,f);Ie=qa(de,ie),g&&wh(f,G,g,r),n!==void 0&&Ih(Ie,this.ngContentSelectors,n),W=Dh(Uu,G,Qe,Ir,S,[Eh]),Mi(de,S,null)}finally{ui()}return new Oo(this.componentType,W,er(Ie,S),S,Ie)}finally{b(i)}}},Oo=class extends _o{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new Ge(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;bi(i[I],i,o,t,n),this.previousInputValues.set(t,n);let s=Ne(this._tNode.index,i);_i(s)}}get injector(){return new Le(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function mh(e,t){let n=e[I],r=ie;return e[r]=t,or(n,r,2,"#host",null)}function yh(e,t,n,r,o,i,s){let a=o[I];vh(r,e,t,s);let c=null;t!==null&&(c=yi(t,o[pt]));let u=i.rendererFactory.createRenderer(t,n),l=16;n.signals?l=4096:n.onPush&&(l=64);let d=rr(o,qc(n),null,l,o[e.index],e,i,u,null,null,c);return a.firstCreatePass&&Eo(a,e,r.length-1),ir(o,d),o[e.index]=d}function vh(e,t,n,r){for(let o of e)t.mergedAttrs=St(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(So(t,t.mergedAttrs,!0),n!==null&&Vc(r,n,t))}function Dh(e,t,n,r,o,i){let s=Y(),a=o[I],c=K(s,o);Zc(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,p=gt(o,a,d,s);We(p,o)}Qc(a,o,s),c&&We(c,o);let u=gt(o,a,s.directiveStart+s.componentOffset,s);if(e[re]=o[re]=u,i!==null)for(let l of i)l(u,t);return zc(a,s,o),u}function wh(e,t,n,r){if(r)so(e,n,["ng-version","17.3.12"]);else{let{attrs:o,classes:i}=hd(t.selectors[0]);o&&so(e,n,o),i&&i.length>0&&jc(e,n,i.join(" "))}}function Ih(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Eh(){let e=Y();fi(P()[I],e)}var cr=(()=>{class e{static{this.__NG_ELEMENT_ID__=Ch}}return e})();function Ch(){let e=Y();return Mh(e,P())}var bh=cr,uu=class extends bh{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return er(this._hostTNode,this._hostLView)}get injector(){return new Le(this._hostTNode,this._hostLView)}get parentInjector(){let t=pi(this._hostTNode,this._hostLView);if(dc(t)){let n=kn(t,this._hostLView),r=Fn(t),o=n[I].data[r+8];return new Le(o,n)}else return new Le(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=na(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Q}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Qs(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Zs(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!Ad(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let c=s?t:new kt(Be(t)),u=r||this.parentInjector;if(!i&&c.ngModule==null){let g=(s?u:this.parentInjector).get(ge,null);g&&(i=g)}let l=Be(c.componentType??{}),d=Qs(this._lContainer,l?.id??null),p=d?.firstChild??null,f=c.create(u,o,p,i);return this.insertImpl(f.hostView,a,Zs(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Vd(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[j],u=new uu(c,c[se],c[j]);u.detach(u.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return zp(s,o,i,r),t.attachToViewContainerRef(),wa(Jr(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=na(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=wo(this._lContainer,n);r&&(xn(Jr(this._lContainer),n),Fc(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=wo(this._lContainer,n);return r&&xn(Jr(this._lContainer),n)!=null?new Ge(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function na(e){return e[On]}function Jr(e){return e[On]||(e[On]=[])}function Mh(e,t){let n,r=t[e.index];return De(r)?n=r:(n=Jc(r,t,null,e),t[e.index]=n,ir(t,n)),Th(n,t,e,r),new uu(n,e,t)}function _h(e,t){let n=e[L],r=n.createComment(""),o=K(t,e),i=wi(n,o);return $n(n,i,r,op(n,o),!1),r}var Th=Sh,xh=()=>!1;function Nh(e,t,n){return xh(e,t,n)}function Sh(e,t,n,r){if(e[He])return;let o;n.type&8?o=ye(r):o=_h(t,n),e[He]=o}function Ah(e){return Object.getPrototypeOf(e.prototype).constructor}function Oh(e){let t=Ah(e.type),n=!0,r=[e];for(;t;){let o;if(_e(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Dn(e.inputs),s.inputTransforms=Dn(e.inputTransforms),s.declaredInputs=Dn(e.declaredInputs),s.outputs=Dn(e.outputs);let a=o.hostBindings;a&&Lh(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Fh(e,c),u&&kh(e,u),Rh(e,o),Rl(e.outputs,o.outputs),_e(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Oh&&(n=!1)}}t=Object.getPrototypeOf(t)}Ph(r)}function Rh(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function Ph(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=St(o.hostAttrs,n=St(n,o.hostAttrs))}}function Dn(e){return e===dt?{}:e===Z?[]:e}function Fh(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function kh(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Lh(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function jh(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var Te=class{},Ro=class{};var Po=class extends Te{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new zn(this);let o=xa(t);this._bootstrapComponents=Ac(o.bootstrap),this._r3Injector=wc(t,n,[{provide:Te,useValue:this},{provide:ar,useValue:this.componentFactoryResolver},...r],B(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Fo=class extends Ro{constructor(t){super(),this.moduleType=t}create(t){return new Po(this.moduleType,t,[])}};var Wn=class extends Te{constructor(t){super(),this.componentFactoryResolver=new zn(this),this.instance=null;let n=new At([...t.providers,{provide:Te,useValue:this},{provide:ar,useValue:this.componentFactoryResolver}],t.parent||ni(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Vh(e,t,n=null){return new Wn({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var ur=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new bt(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function lu(e){return $h(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Bh(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function $h(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function du(e,t,n){return e[t]=n}function xe(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function fu(e,t,n,r){let o=xe(e,t,n);return xe(e,t+1,r)||o}function Hh(e,t,n,r,o){let i=fu(e,t,n,r);return xe(e,t+2,o)||i}function Uh(e){return(e.flags&32)===32}function zh(e,t,n,r,o,i,s,a,c){let u=t.consts,l=or(t,e,4,s||null,Pn(u,a));Yc(t,n,l,Pn(u,c)),fi(t,l);let d=l.tView=Ci(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,u,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function Wh(e,t,n,r,o,i,s,a){let c=P(),u=X(),l=e+ie,d=u.firstCreatePass?zh(l,u,c,t,n,r,o,i,s):u.data[l];Bt(d,!1);let p=Gh(u,c,d,e);li()&&Ii(u,c,p,d),We(p,c);let f=Jc(p,c,p,d);return c[l]=f,ir(c,f),Nh(f,d,c),oi(d)&&Wc(u,c,d),s!=null&&Gc(c,d,a),Wh}var Gh=qh;function qh(e,t,n,r){return di(!0),t[L].createComment("")}function Yh(e,t,n,r){let o=P(),i=ai();if(xe(o,i,t)){let s=X(),a=cc();Pp(a,o,e,t,n,r)}return Yh}function Zh(e,t,n,r){return xe(e,ai(),n)?t+lt(n)+r:we}function Qh(e,t,n,r,o,i){let s=Jd(),a=fu(e,s,n,o);return tc(2),a?t+lt(n)+r+lt(o)+i:we}function wn(e,t){return e<<17|t<<2}function qe(e){return e>>17&32767}function Jh(e){return(e&2)==2}function Kh(e,t){return e&131071|t<<17}function ko(e){return e|2}function yt(e){return(e&131068)>>2}function Kr(e,t){return e&-131069|t<<2}function Xh(e){return(e&1)===1}function Lo(e){return e|1}function eg(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=qe(s),c=yt(s);e[r]=n;let u=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Vt(d,l)>0)&&(u=!0)}else l=n;if(o)if(c!==0){let p=qe(e[a+1]);e[r+1]=wn(p,a),p!==0&&(e[p+1]=Kr(e[p+1],r)),e[a+1]=Kh(e[a+1],r)}else e[r+1]=wn(a,0),a!==0&&(e[a+1]=Kr(e[a+1],r)),a=r;else e[r+1]=wn(c,0),a===0?a=r:e[c+1]=Kr(e[c+1],r),c=r;u&&(e[r+1]=ko(e[r+1])),ra(e,l,r,!0),ra(e,l,r,!1),tg(t,l,e,r,i),s=wn(a,c),i?t.classBindings=s:t.styleBindings=s}function tg(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Vt(i,t)>=0&&(n[r+1]=Lo(n[r+1]))}function ra(e,t,n,r){let o=e[n+1],i=t===null,s=r?qe(o):yt(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];ng(c,t)&&(a=!0,e[s+1]=r?Lo(u):ko(u)),s=r?qe(u):yt(u)}a&&(e[n+1]=r?ko(o):Lo(o))}function ng(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Vt(e,t)>=0:!1}function rg(e,t,n){let r=P(),o=ai();if(xe(r,o,t)){let i=X(),s=cc();Ep(i,s,r,e,t,r[L],n,!1)}return rg}function oa(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";bi(e,n,i[s],s,r)}function og(e,t){return ig(e,t,null,!0),og}function ig(e,t,n,r){let o=P(),i=X(),s=tc(2);if(i.firstUpdatePass&&ag(i,e,s,r),t!==we&&xe(o,s,t)){let a=i.data[Ye()];fg(i,a,o,o[L],e,o[s+1]=pg(t,n),r,s)}}function sg(e,t){return t>=e.expandoStartIndex}function ag(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Ye()],s=sg(e,n);hg(i,r)&&t===null&&!s&&(t=!1),t=cg(o,i,t,r),eg(o,i,t,n,s,r)}}function cg(e,t,n,r){let o=nf(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Xr(null,e,t,n,r),n=Lt(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Xr(o,e,t,n,r),i===null){let c=ug(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Xr(null,e,t,c[1],r),c=Lt(c,t.attrs,r),lg(e,t,r,c))}else i=dg(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function ug(e,t,n){let r=n?t.classBindings:t.styleBindings;if(yt(r)!==0)return e[qe(r)]}function lg(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[qe(o)]=r}function dg(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Lt(r,s,n)}return Lt(r,t.attrs,n)}function Xr(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Lt(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Lt(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),td(e,s,n?!0:t[++i]))}return e===void 0?null:e}function fg(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,u=c[a+1],l=Xh(u)?ia(c,t,n,o,yt(u),s):void 0;if(!Gn(l)){Gn(i)||Jh(u)&&(i=ia(c,null,n,o,a,s));let d=Ga(Ye(),n);dp(r,s,d,o,i)}}function ia(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,p=n[o+1];p===we&&(p=d?Z:void 0);let f=d?Hr(p,r):l===r?p:void 0;if(u&&!Gn(f)&&(f=Hr(c,r)),Gn(f)&&(a=f,s))return a;let h=e[o+1];o=s?qe(h):yt(h)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Hr(c,r))}return a}function Gn(e){return e!==void 0}function pg(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=B(tr(e)))),e}function hg(e,t){return(e.flags&(t?8:16))!==0}function gg(e,t,n,r,o,i){let s=t.consts,a=Pn(s,o),c=or(t,e,2,r,a);return Yc(t,n,c,Pn(s,i)),c.attrs!==null&&So(c,c.attrs,!1),c.mergedAttrs!==null&&So(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function pu(e,t,n,r){let o=P(),i=X(),s=ie+e,a=o[L],c=i.firstCreatePass?gg(s,i,o,t,n,r):i.data[s],u=yg(i,o,c,a,t,e);o[s]=u;let l=oi(c);return Bt(c,!0),Vc(a,u,c),!Uh(c)&&li()&&Ii(i,o,u,c),Ud()===0&&We(u,o),zd(),l&&(Wc(i,o,c),zc(i,c,o)),r!==null&&Gc(o,c),pu}function hu(){let e=Y();Xa()?Qd():(e=e.parent,Bt(e,!1));let t=e;qd(t)&&Yd(),Wd();let n=X();return n.firstCreatePass&&(fi(n,e),Va(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&df(t)&&oa(n,t,P(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&ff(t)&&oa(n,t,P(),t.stylesWithoutHost,!1),hu}function mg(e,t,n,r){return pu(e,t,n,r),hu(),mg}var yg=(e,t,n,r,o,i)=>(di(!0),Oc(r,o,af()));function WE(){return P()}var qn="en-US";var vg=qn;function Dg(e){typeof e=="string"&&(vg=e.toLowerCase().replace(/_/g,"-"))}function wg(e,t,n,r){let o=P(),i=X(),s=Y();return Eg(i,o,o[L],s,e,t,r),wg}function Ig(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Ot],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Eg(e,t,n,r,o,i,s){let a=oi(r),u=e.firstCreatePass&&Vp(e),l=t[re],d=jp(t),p=!0;if(r.type&3||s){let g=K(r,t),v=s?s(g):g,T=d.length,de=s?W=>s(ye(W[r.index])):r.index,S=null;if(!s&&a&&(S=Ig(e,t,o,r.index)),S!==null){let W=S.__ngLastListenerFn__||S;W.__ngNextListenerFn__=i,S.__ngLastListenerFn__=i,p=!1}else{i=aa(r,t,l,i,!1);let W=n.listen(v,o,i);d.push(i,W),u&&u.push(o,de,T,T+1)}}else i=aa(r,t,l,i,!1);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let g=h.length;if(g)for(let v=0;v<g;v+=2){let T=h[v],de=h[v+1],Ie=t[T][de].subscribe(i),G=d.length;d.push(i,Ie),u&&u.push(o,r.index,G,-(G+1))}}}function sa(e,t,n,r){let o=b(null);try{return pe(6,t,n),n(r)!==!1}catch(i){return Xc(e,i),!1}finally{pe(7,t,n),b(o)}}function aa(e,t,n,r,o){return function i(s){if(s===Function)return r;let a=e.componentOffset>-1?Ne(e.index,t):t;_i(a);let c=sa(t,n,r,s),u=i.__ngNextListenerFn__;for(;u;)c=sa(t,n,u,s)&&c,u=u.__ngNextListenerFn__;return o&&c===!1&&s.preventDefault(),c}}function GE(e=1){return of(e)}function Cg(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function qE(e,t=""){let n=P(),r=X(),o=e+ie,i=r.firstCreatePass?or(r,o,1,t,null):r.data[o],s=bg(r,n,i,t,e);n[o]=s,li()&&Ii(r,n,s,i),Bt(i,!1)}var bg=(e,t,n,r,o)=>(di(!0),Gf(t[L],r));function Mg(e){return gu("",e,""),Mg}function gu(e,t,n){let r=P(),o=Zh(r,e,t,n);return o!==we&&eu(r,Ye(),o),gu}function _g(e,t,n,r,o){let i=P(),s=Qh(i,e,t,n,r,o);return s!==we&&eu(i,Ye(),s),_g}function Tg(e,t,n){let r=X();if(r.firstCreatePass){let o=_e(e);jo(n,r.data,r.blueprint,o,!0),jo(t,r.data,r.blueprint,o,!1)}}function jo(e,t,n,r,o){if(e=V(e),Array.isArray(e))for(let i=0;i<e.length;i++)jo(e[i],t,n,r,o);else{let i=X(),s=P(),a=Y(),c=ft(e)?e:V(e.provide),u=Fa(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(ft(e)||!e.multi){let f=new ze(u,o,ae),h=to(c,t,o?l:l+p,d);h===-1?(go(jn(a,s),i,c),eo(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=to(c,t,l+p,d),h=to(c,t,l,l+p),g=f>=0&&n[f],v=h>=0&&n[h];if(o&&!v||!o&&!g){go(jn(a,s),i,c);let T=Sg(o?Ng:xg,n.length,o,r,u);!o&&v&&(n[h].providerFactory=T),eo(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(T),s.push(T)}else{let T=mu(n[o?h:f],u,!o&&r);eo(i,e,f>-1?f:h,T)}!o&&r&&v&&n[h].componentProviders++}}}function eo(e,t,n,r){let o=ft(t),i=Cd(t);if(o||i){let c=(i?V(t.useClass):t).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=u.indexOf(n);l===-1?u.push(n,[r,c]):u[l+1].push(r,c)}else u.push(n,c)}}}function mu(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function to(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function xg(e,t,n,r){return Vo(this.multi,[])}function Ng(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=gt(n,n[I],this.providerFactory.index,r);i=a.slice(0,s),Vo(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Vo(o,i);return i}function Vo(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Sg(e,t,n,r,o){let i=new ze(e,n,ae);return i.multi=[],i.index=t,i.componentProviders=0,mu(i,o,r&&!n),i}function YE(e,t=[]){return n=>{n.providersResolver=(r,o)=>Tg(r,o?o(e):e,t)}}var Ag=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Aa(!1,n.type),o=r.length>0?Vh([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=A({token:e,providedIn:"environment",factory:()=>new e(N(ge))})}}return e})();function ZE(e){Ti("NgStandalone"),e.getStandaloneInjector=t=>t.get(Ag).getOrCreateStandaloneInjector(e)}function yu(e,t){let n=e[t];return n===we?void 0:n}function Og(e,t,n,r,o,i){let s=t+n;return xe(e,s,o)?du(e,s+1,i?r.call(i,o):r(o)):yu(e,s+1)}function Rg(e,t,n,r,o,i,s,a){let c=t+n;return Hh(e,c,o,i,s)?du(e,c+3,a?r.call(a,o,i,s):r(o,i,s)):yu(e,c+3)}function QE(e,t){let n=X(),r,o=e+ie;n.firstCreatePass?(r=Pg(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=je(r.type,!0)),s,a=U(ae);try{let c=Ln(!1),u=i();return Ln(c),Cg(n,P(),o,u),u}finally{U(a)}}function Pg(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function JE(e,t,n){let r=e+ie,o=P(),i=Ya(o,r);return vu(o,r)?Og(o,ec(),t,i.transform,n,i):i.transform(n)}function KE(e,t,n,r,o){let i=e+ie,s=P(),a=Ya(s,i);return vu(s,i)?Rg(s,ec(),t,a.transform,n,r,o,a):a.transform(n,r,o)}function vu(e,t){return e[I].data[t].pure}var XE=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Fg=new x("");function lr(e){return!!e&&typeof e.then=="function"}function Ni(e){return!!e&&typeof e.subscribe=="function"}var kg=new x(""),Du=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=O(kg,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(lr(i))n.push(i);else if(Ni(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),wu=new x("");function Lg(){Xi(()=>{throw new _(600,!1)})}function jg(e){return e.isBoundToModule}function Vg(e,t,n){try{let r=n();return lr(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}var Si=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=O(Ic),this.afterRenderEffectManager=O(cu),this.externalTestViews=new Set,this.beforeRender=new Ee,this.afterTick=new Ee,this.componentTypes=[],this.components=[],this.isStable=O(ur).hasPendingTasks.pipe(H(n=>!n)),this._injector=O(ge)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Un;if(!this._injector.get(Du).done){let p=!o&&yd(n),f=!1;throw new _(405,f)}let s;o?s=n:s=this._injector.get(ar).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=jg(s)?void 0:this._injector.get(Te),c=r||s.selector,u=s.create(hi.NULL,[],c,a),l=u.location.nativeElement,d=u.injector.get(Fg,null);return d?.registerApplication(l),u.onDestroy(()=>{this.detachView(u.hostView),no(this.components,u),d?.unregisterApplication(l)}),this._loadComponent(u),u}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new _(101,!1);let r=b(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this.afterTick.next(),this._runningTick=!1,b(r)}}detectChangesInAttachedViews(n){let r=0,o=this.afterRenderEffectManager;for(;;){if(r===nu)throw new _(103,!1);if(n){let i=r===0;this.beforeRender.next(i);for(let{_lView:s,notifyErrorHandler:a}of this._views)Bg(s,i,a)}if(r++,o.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>Bo(i))&&(o.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>Bo(i))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;no(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(wu,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>no(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function no(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Bg(e,t,n){!t&&!Bo(e)||$g(e,n,t)}function Bo(e){return si(e)}function $g(e,t,n){let r;n?(r=0,e[m]|=1024):e[m]&64?r=0:r=1,ru(e,t,r)}var $o=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},eC=(()=>{class e{compileModuleSync(n){return new Fo(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=xa(n),i=Ac(o.declarations).reduce((s,a)=>{let c=Be(a);return c&&s.push(new kt(c)),s},[]);return new $o(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Hg=(()=>{class e{constructor(){this.zone=O(J),this.applicationRef=O(Si)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Ug(e){return[{provide:J,useFactory:e},{provide:Nn,multi:!0,useFactory:()=>{let t=O(Hg,{optional:!0});return()=>t.initialize()}},{provide:Nn,multi:!0,useFactory:()=>{let t=O(qg);return()=>{t.initialize()}}},{provide:Ic,useFactory:zg}]}function zg(){let e=O(J),t=O(mt);return n=>e.runOutsideAngular(()=>t.handleError(n))}function Wg(e){let t=Ug(()=>new J(Gg(e)));return ei([[],t])}function Gg(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var qg=(()=>{class e{constructor(){this.subscription=new k,this.initialized=!1,this.zone=O(J),this.pendingTasks=O(ur)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{J.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{J.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Yg(){return typeof $localize<"u"&&$localize.locale||qn}var Ai=new x("",{providedIn:"root",factory:()=>O(Ai,E.Optional|E.SkipSelf)||Yg()});var Iu=new x("");var Mn=null;function Zg(e=[],t){return hi.create({name:t,providers:[{provide:Pa,useValue:"platform"},{provide:Iu,useValue:new Set([()=>Mn=null])},...e]})}function Qg(e=[]){if(Mn)return Mn;let t=Zg(e);return Mn=t,Lg(),Jg(t),t}function Jg(e){e.get(Rf,null)?.forEach(n=>n())}var Oi=(()=>{class e{static{this.__NG_ELEMENT_ID__=Kg}}return e})();function Kg(e){return Xg(Y(),P(),(e&16)===16)}function Xg(e,t,n){if(Xn(e)&&!n){let r=Ne(e.index,t);return new Ge(r,r)}else if(e.type&47){let r=t[me];return new Ge(r,t)}return null}var Ho=class{constructor(){}supports(t){return lu(t)}create(t){return new Uo(t)}},em=(e,t)=>t,Uo=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||em}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<ca(r,o,i)?n:r,a=ca(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let p=0;p<u;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;l<=h&&h<u&&(i[p]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!lu(t))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Bh(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new zo(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Yn),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Yn),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},zo=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Wo=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Yn=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Wo,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ca(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function ua(){return new Ri([new Ho])}var Ri=(()=>{class e{static{this.\u0275prov=A({token:e,providedIn:"root",factory:ua})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||ua()),deps:[[e,new Xl,new Kl]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new _(901,!1)}}return e})();function tC(e){try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=Qg(r),i=[Wg(),...n||[]],a=new Wn({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1}).injector,c=a.get(J);return c.run(()=>{a.resolveInjectorInitializers();let u=a.get(mt,null),l;c.runOutsideAngular(()=>{l=c.onError.subscribe({next:f=>{u.handleError(f)}})});let d=()=>a.destroy(),p=o.get(Iu);return p.add(d),a.onDestroy(()=>{l.unsubscribe(),p.delete(d)}),Vg(u,c,()=>{let f=a.get(Du);return f.runInitializers(),f.donePromise.then(()=>{let h=a.get(Ai,qn);Dg(h||qn);let g=a.get(Si);return t!==void 0&&g.bootstrap(t),g})})})}catch(t){return Promise.reject(t)}}function tm(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Pi(e){let t=b(null);try{return e()}finally{b(t)}}var Tu=null;function Fi(){return Tu}function _C(e){Tu??=e}var Eu=class{};var pr=new x(""),xu=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:()=>O(nm),providedIn:"platform"})}}return e})();var nm=(()=>{class e extends xu{constructor(){super(),this._doc=O(pr),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Fi().getBaseHref(this._doc)}onPopState(n){let r=Fi().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Fi().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Nu(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function Cu(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function Ze(e){return e&&e[0]!=="?"?"?"+e:e}var Bi=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=A({token:e,factory:()=>O(om),providedIn:"root"})}}return e})(),rm=new x(""),om=(()=>{class e extends Bi{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??O(pr).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Nu(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Ze(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Ze(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Ze(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(N(xu),N(rm,8))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var im=(()=>{class e{constructor(n){this._subject=new it,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=cm(Cu(bu(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Ze(r))}normalize(n){return e.stripTrailingSlash(am(this._basePath,bu(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ze(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Ze(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=Ze}static{this.joinWithSlash=Nu}static{this.stripTrailingSlash=Cu}static{this.\u0275fac=function(r){return new(r||e)(N(Bi))}}static{this.\u0275prov=A({token:e,factory:()=>sm(),providedIn:"root"})}}return e})();function sm(){return new im(N(Bi))}function am(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function bu(e){return e.replace(/\/index.html$/,"")}function cm(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}function Su(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ki=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},TC=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new ki(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Mu(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Mu(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(ae(cr),ae(sr),ae(Ri))}}static{this.\u0275dir=Ko({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function Mu(e,t){e.context.$implicit=t.item}var xC=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Li,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){_u("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){_u("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(ae(cr),ae(sr))}}static{this.\u0275dir=Ko({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Li=class{constructor(){this.$implicit=null,this.ngIf=null}};function _u(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${B(t)}'.`)}function Au(e,t){return new _(2100,!1)}var ji=class{createSubscription(t,n){return Pi(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){Pi(()=>t.unsubscribe())}},Vi=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},um=new Vi,lm=new ji,NC=(()=>{class e{constructor(n){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(lr(n))return um;if(Ni(n))return lm;throw Au(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static{this.\u0275fac=function(r){return new(r||e)(ae(Oi,16))}}static{this.\u0275pipe=Xo({name:"async",type:e,pure:!1,standalone:!0})}}return e})();var SC=(()=>{class e{transform(n,r,o){if(n==null)return null;if(!this.supports(n))throw Au(e,n);return n.slice(r,o)}supports(n){return typeof n=="string"||Array.isArray(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275pipe=Xo({name:"slice",type:e,pure:!1,standalone:!0})}}return e})();var AC=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=Jo({type:e})}static{this.\u0275inj=qo({})}}return e})(),OC="browser",dm="server";function fm(e){return e===dm}var fr=class{};var Ht=class{},gr=class{},ue=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Hi=class{encodeKey(t){return Ou(t)}encodeValue(t){return Ou(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function hm(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var gm=/%(\d[a-f0-9])/gi,mm={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Ou(e){return encodeURIComponent(e).replace(gm,(t,n)=>mm[n]??t)}function hr(e){return`${e}`}var ce=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new Hi,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=hm(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(hr):[hr(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(hr(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(hr(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var z=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function ym(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Ru(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Pu(e){return typeof Blob<"u"&&e instanceof Blob}function Fu(e){return typeof FormData<"u"&&e instanceof FormData}function vm(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var $t=class e{constructor(t,n,r,o){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let i;if(ym(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new ue,this.context??=new z,!this.params)this.params=new ce,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Ru(this.body)||Pu(this.body)||Fu(this.body)||vm(this.body)?this.body:this.body instanceof ce?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Fu(this.body)?null:Pu(this.body)?this.body.type||null:Ru(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof ce?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,u=t.headers||this.headers,l=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(u=Object.keys(t.setHeaders).reduce((p,f)=>p.set(f,t.setHeaders[f]),u)),t.setParams&&(l=Object.keys(t.setParams).reduce((p,f)=>p.set(f,t.setParams[f]),l)),new e(n,r,s,{params:l,headers:u,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Dt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Dt||{}),Ut=class{constructor(t,n=vr.Ok,r="OK"){this.headers=t.headers||new ue,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Ui=class e extends Ut{constructor(t={}){super(t),this.type=Dt.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},mr=class e extends Ut{constructor(t={}){super(t),this.type=Dt.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},yr=class extends Ut{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},vr=function(e){return e[e.Continue=100]="Continue",e[e.SwitchingProtocols=101]="SwitchingProtocols",e[e.Processing=102]="Processing",e[e.EarlyHints=103]="EarlyHints",e[e.Ok=200]="Ok",e[e.Created=201]="Created",e[e.Accepted=202]="Accepted",e[e.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",e[e.NoContent=204]="NoContent",e[e.ResetContent=205]="ResetContent",e[e.PartialContent=206]="PartialContent",e[e.MultiStatus=207]="MultiStatus",e[e.AlreadyReported=208]="AlreadyReported",e[e.ImUsed=226]="ImUsed",e[e.MultipleChoices=300]="MultipleChoices",e[e.MovedPermanently=301]="MovedPermanently",e[e.Found=302]="Found",e[e.SeeOther=303]="SeeOther",e[e.NotModified=304]="NotModified",e[e.UseProxy=305]="UseProxy",e[e.Unused=306]="Unused",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.PaymentRequired=402]="PaymentRequired",e[e.Forbidden=403]="Forbidden",e[e.NotFound=404]="NotFound",e[e.MethodNotAllowed=405]="MethodNotAllowed",e[e.NotAcceptable=406]="NotAcceptable",e[e.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",e[e.RequestTimeout=408]="RequestTimeout",e[e.Conflict=409]="Conflict",e[e.Gone=410]="Gone",e[e.LengthRequired=411]="LengthRequired",e[e.PreconditionFailed=412]="PreconditionFailed",e[e.PayloadTooLarge=413]="PayloadTooLarge",e[e.UriTooLong=414]="UriTooLong",e[e.UnsupportedMediaType=415]="UnsupportedMediaType",e[e.RangeNotSatisfiable=416]="RangeNotSatisfiable",e[e.ExpectationFailed=417]="ExpectationFailed",e[e.ImATeapot=418]="ImATeapot",e[e.MisdirectedRequest=421]="MisdirectedRequest",e[e.UnprocessableEntity=422]="UnprocessableEntity",e[e.Locked=423]="Locked",e[e.FailedDependency=424]="FailedDependency",e[e.TooEarly=425]="TooEarly",e[e.UpgradeRequired=426]="UpgradeRequired",e[e.PreconditionRequired=428]="PreconditionRequired",e[e.TooManyRequests=429]="TooManyRequests",e[e.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",e[e.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",e[e.InternalServerError=500]="InternalServerError",e[e.NotImplemented=501]="NotImplemented",e[e.BadGateway=502]="BadGateway",e[e.ServiceUnavailable=503]="ServiceUnavailable",e[e.GatewayTimeout=504]="GatewayTimeout",e[e.HttpVersionNotSupported=505]="HttpVersionNotSupported",e[e.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",e[e.InsufficientStorage=507]="InsufficientStorage",e[e.LoopDetected=508]="LoopDetected",e[e.NotExtended=510]="NotExtended",e[e.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",e}(vr||{});function $i(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var zt=(()=>{class e{constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof $t)i=n;else{let c;o.headers instanceof ue?c=o.headers:c=new ue(o.headers);let u;o.params&&(o.params instanceof ce?u=o.params:u=new ce({fromObject:o.params})),i=new $t(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=dn(i).pipe(kr(c=>this.handler.handle(c)));if(n instanceof $t||o.observe==="events")return s;let a=s.pipe(Fe(c=>c instanceof mr));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(H(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return a.pipe(H(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return a.pipe(H(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return a.pipe(H(c=>c.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new ce().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,$i(o,r))}post(n,r,o={}){return this.request("POST",n,$i(o,r))}put(n,r,o={}){return this.request("PUT",n,$i(o,r))}static{this.\u0275fac=function(r){return new(r||e)(N(Ht))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac})}}return e})();function Dm(e,t){return t(e)}function wm(e,t,n){return(r,o)=>ka(n,()=>t(r,i=>e(i,o)))}var zi=new x(""),Im=new x(""),Em=new x("");var ku=(()=>{class e extends Ht{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=O(ur);let o=O(Em,{optional:!0});this.backend=o??n}handle(n){if(this.chain===null){let o=Array.from(new Set([...this.injector.get(zi),...this.injector.get(Im,[])]));this.chain=o.reduceRight((i,s)=>wm(i,s,this.injector),Dm)}let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(jr(()=>this.pendingTasks.remove(r)))}static{this.\u0275fac=function(r){return new(r||e)(N(gr),N(ge))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac})}}return e})();var Cm=/^\)\]\}',?\n/;function bm(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var Lu=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new _(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?fe(r.\u0275loadImpl()):dn(null)).pipe(Br(()=>new M(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,v)=>s.setRequestHeader(g,v.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let g=n.detectContentTypeHeader();g!==null&&s.setRequestHeader("Content-Type",g)}if(n.responseType){let g=n.responseType.toLowerCase();s.responseType=g!=="json"?g:"text"}let a=n.serializeBody(),c=null,u=()=>{if(c!==null)return c;let g=s.statusText||"OK",v=new ue(s.getAllResponseHeaders()),T=bm(s)||n.url;return c=new Ui({headers:v,status:s.status,statusText:g,url:T}),c},l=()=>{let{headers:g,status:v,statusText:T,url:de}=u(),S=null;v!==vr.NoContent&&(S=typeof s.response>"u"?s.responseText:s.response),v===0&&(v=S?vr.Ok:0);let W=v>=200&&v<300;if(n.responseType==="json"&&typeof S=="string"){let Ie=S;S=S.replace(Cm,"");try{S=S!==""?JSON.parse(S):null}catch(G){S=Ie,W&&(W=!1,S={error:G,text:S})}}W?(i.next(new mr({body:S,headers:g,status:v,statusText:T,url:de||void 0})),i.complete()):i.error(new yr({error:S,headers:g,status:v,statusText:T,url:de||void 0}))},d=g=>{let{url:v}=u(),T=new yr({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:v||void 0});i.error(T)},p=!1,f=g=>{p||(i.next(u()),p=!0);let v={type:Dt.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),n.responseType==="text"&&s.responseText&&(v.partialText=s.responseText),i.next(v)},h=g=>{let v={type:Dt.UploadProgress,loaded:g.loaded};g.lengthComputable&&(v.total=g.total),i.next(v)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",h)),s.send(a),i.next({type:Dt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",h)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(N(fr))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac})}}return e})(),ju=new x(""),Mm="XSRF-TOKEN",_m=new x("",{providedIn:"root",factory:()=>Mm}),Tm="X-XSRF-TOKEN",xm=new x("",{providedIn:"root",factory:()=>Tm}),Dr=class{},Nm=(()=>{class e{constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Su(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(N(pr),N(mi),N(_m))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac})}}return e})();function Sm(e,t){let n=e.url.toLowerCase();if(!O(ju)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=O(Dr).getToken(),o=O(xm);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Vu=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Vu||{});function Am(e,t){return{\u0275kind:e,\u0275providers:t}}function qC(...e){let t=[zt,Lu,ku,{provide:Ht,useExisting:ku},{provide:gr,useExisting:Lu},{provide:zi,useValue:Sm,multi:!0},{provide:ju,useValue:!0},{provide:Dr,useClass:Nm}];for(let n of e)t.push(...n.\u0275providers);return ei(t)}function YC(e){return Am(Vu.Interceptors,e.map(t=>({provide:zi,useValue:t,multi:!0})))}var wt=class{encodeKey(t){return encodeURIComponent(t)}encodeValue(t){return encodeURIComponent(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};var wr=new x("basePath");var le=class{constructor(t={}){this.apiKeys=t.apiKeys,this.username=t.username,this.password=t.password,this.accessToken=t.accessToken,this.basePath=t.basePath,this.withCredentials=t.withCredentials,this.encoder=t.encoder,t.encodeParam?this.encodeParam=t.encodeParam:this.encodeParam=n=>this.defaultEncodeParam(n),t.credentials?this.credentials=t.credentials:this.credentials={}}selectHeaderContentType(t){if(t.length===0)return;let n=t.find(r=>this.isJsonMime(r));return n===void 0?t[0]:n}selectHeaderAccept(t){if(t.length===0)return;let n=t.find(r=>this.isJsonMime(r));return n===void 0?t[0]:n}isJsonMime(t){let n=new RegExp("^(application/json|[^;/ 	]+/[^;/ 	]+[+]json)[ 	]*(;.*)?$","i");return t!==null&&(n.test(t)||t.toLowerCase()==="application/json-patch+json")}lookupCredential(t){let n=this.credentials[t];return typeof n=="function"?n():n}defaultEncodeParam(t){let n=t.dataFormat==="date-time"&&t.value instanceof Date?t.value.toISOString():t.value;return encodeURIComponent(String(n))}};var Om=(()=>{class e{constructor(n,r,o){this.httpClient=n,this.basePath="http://localhost:8083/JobUp",this.defaultHeaders=new ue,this.configuration=new le,o&&(this.configuration=o),typeof this.configuration.basePath!="string"&&(Array.isArray(r)&&r.length>0&&(r=r[0]),typeof r!="string"&&(r=this.basePath),this.configuration.basePath=r),this.encoder=this.configuration.encoder||new wt}addToHttpParams(n,r,o){return typeof r=="object"&&!(r instanceof Date)?n=this.addToHttpParamsRecursive(n,r):n=this.addToHttpParamsRecursive(n,r,o),n}addToHttpParamsRecursive(n,r,o){if(r==null)return n;if(typeof r=="object")if(Array.isArray(r))r.forEach(i=>n=this.addToHttpParamsRecursive(n,i,o));else if(r instanceof Date)if(o!=null)n=n.append(o,r.toISOString().substring(0,10));else throw Error("key may not be null if value is Date");else Object.keys(r).forEach(i=>n=this.addToHttpParamsRecursive(n,r[i],o!=null?`${o}.${i}`:i));else if(o!=null)n=n.append(o,r);else throw Error("key may not be null if value is not object or array");return n}createWorker(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter workerCreateDto was null or undefined when calling createWorker.");let s=this.defaultHeaders,a=i&&i.httpHeaderAccept;if(a===void 0){let f=["application/json"];a=this.configuration.selectHeaderAccept(f)}a!==void 0&&(s=s.set("Accept",a));let c=i&&i.context;c===void 0&&(c=new z);let u=["application/json"],l=this.configuration.selectHeaderContentType(u);l!==void 0&&(s=s.set("Content-Type",l));let d="json";return a&&(a.startsWith("text")?d="text":this.configuration.isJsonMime(a)?d="json":d="blob"),this.httpClient.request("post",`${this.configuration.basePath}/api/workers`,{context:c,body:n,responseType:d,withCredentials:this.configuration.withCredentials,headers:s,observe:r,reportProgress:o})}deleteWorker(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter id was null or undefined when calling deleteWorker.");let s=this.defaultHeaders,a=i&&i.httpHeaderAccept;if(a===void 0){let d=[];a=this.configuration.selectHeaderAccept(d)}a!==void 0&&(s=s.set("Accept",a));let c=i&&i.context;c===void 0&&(c=new z);let u="json";a&&(a.startsWith("text")?u="text":this.configuration.isJsonMime(a)?u="json":u="blob");let l=`/api/workers/${this.configuration.encodeParam({name:"id",value:n,in:"path",style:"simple",explode:!1,dataType:"string",dataFormat:void 0})}`;return this.httpClient.request("delete",`${this.configuration.basePath}${l}`,{context:c,responseType:u,withCredentials:this.configuration.withCredentials,headers:s,observe:r,reportProgress:o})}getAllWorkers(n="body",r=!1,o){let i=this.defaultHeaders,s=o&&o.httpHeaderAccept;if(s===void 0){let l=["application/json"];s=this.configuration.selectHeaderAccept(l)}s!==void 0&&(i=i.set("Accept",s));let a=o&&o.context;a===void 0&&(a=new z);let c="json";return s&&(s.startsWith("text")?c="text":this.configuration.isJsonMime(s)?c="json":c="blob"),this.httpClient.request("get",`${this.configuration.basePath}/api/workers`,{context:a,responseType:c,withCredentials:this.configuration.withCredentials,headers:i,observe:n,reportProgress:r})}getWorkerById(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter id was null or undefined when calling getWorkerById.");let s=this.defaultHeaders,a=i&&i.httpHeaderAccept;if(a===void 0){let d=["application/json"];a=this.configuration.selectHeaderAccept(d)}a!==void 0&&(s=s.set("Accept",a));let c=i&&i.context;c===void 0&&(c=new z);let u="json";a&&(a.startsWith("text")?u="text":this.configuration.isJsonMime(a)?u="json":u="blob");let l=`/api/workers/${this.configuration.encodeParam({name:"id",value:n,in:"path",style:"simple",explode:!1,dataType:"string",dataFormat:void 0})}`;return this.httpClient.request("get",`${this.configuration.basePath}${l}`,{context:c,responseType:u,withCredentials:this.configuration.withCredentials,headers:s,observe:r,reportProgress:o})}searchByJobType(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter jobType was null or undefined when calling searchByJobType.");let s=new ce({encoder:this.encoder});n!=null&&(s=this.addToHttpParams(s,n,"jobType"));let a=this.defaultHeaders,c=i&&i.httpHeaderAccept;if(c===void 0){let p=["application/json"];c=this.configuration.selectHeaderAccept(p)}c!==void 0&&(a=a.set("Accept",c));let u=i&&i.context;u===void 0&&(u=new z);let l="json";return c&&(c.startsWith("text")?l="text":this.configuration.isJsonMime(c)?l="json":l="blob"),this.httpClient.request("get",`${this.configuration.basePath}/api/workers/search/job`,{context:u,params:s,responseType:l,withCredentials:this.configuration.withCredentials,headers:a,observe:r,reportProgress:o})}searchByLocation(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter location was null or undefined when calling searchByLocation.");let s=new ce({encoder:this.encoder});n!=null&&(s=this.addToHttpParams(s,n,"location"));let a=this.defaultHeaders,c=i&&i.httpHeaderAccept;if(c===void 0){let p=["application/json"];c=this.configuration.selectHeaderAccept(p)}c!==void 0&&(a=a.set("Accept",c));let u=i&&i.context;u===void 0&&(u=new z);let l="json";return c&&(c.startsWith("text")?l="text":this.configuration.isJsonMime(c)?l="json":l="blob"),this.httpClient.request("get",`${this.configuration.basePath}/api/workers/search/location`,{context:u,params:s,responseType:l,withCredentials:this.configuration.withCredentials,headers:a,observe:r,reportProgress:o})}updateWorker(n,r,o="body",i=!1,s){if(n==null)throw new Error("Required parameter id was null or undefined when calling updateWorker.");if(r==null)throw new Error("Required parameter workerUpdateDto was null or undefined when calling updateWorker.");let a=this.defaultHeaders,c=s&&s.httpHeaderAccept;if(c===void 0){let h=["application/json"];c=this.configuration.selectHeaderAccept(h)}c!==void 0&&(a=a.set("Accept",c));let u=s&&s.context;u===void 0&&(u=new z);let l=["application/json"],d=this.configuration.selectHeaderContentType(l);d!==void 0&&(a=a.set("Content-Type",d));let p="json";c&&(c.startsWith("text")?p="text":this.configuration.isJsonMime(c)?p="json":p="blob");let f=`/api/workers/${this.configuration.encodeParam({name:"id",value:n,in:"path",style:"simple",explode:!1,dataType:"string",dataFormat:void 0})}`;return this.httpClient.request("put",`${this.configuration.basePath}${f}`,{context:u,body:r,responseType:p,withCredentials:this.configuration.withCredentials,headers:a,observe:o,reportProgress:i})}static{this.\u0275fac=function(r){return new(r||e)(N(zt),N(wr,8),N(le,8))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Rm=(()=>{class e{constructor(n,r,o){this.httpClient=n,this.basePath="http://localhost:8083/JobUp",this.defaultHeaders=new ue,this.configuration=new le,o&&(this.configuration=o),typeof this.configuration.basePath!="string"&&(Array.isArray(r)&&r.length>0&&(r=r[0]),typeof r!="string"&&(r=this.basePath),this.configuration.basePath=r),this.encoder=this.configuration.encoder||new wt}addToHttpParams(n,r,o){return typeof r=="object"&&!(r instanceof Date)?n=this.addToHttpParamsRecursive(n,r):n=this.addToHttpParamsRecursive(n,r,o),n}addToHttpParamsRecursive(n,r,o){if(r==null)return n;if(typeof r=="object")if(Array.isArray(r))r.forEach(i=>n=this.addToHttpParamsRecursive(n,i,o));else if(r instanceof Date)if(o!=null)n=n.append(o,r.toISOString().substring(0,10));else throw Error("key may not be null if value is Date");else Object.keys(r).forEach(i=>n=this.addToHttpParamsRecursive(n,r[i],o!=null?`${o}.${i}`:i));else if(o!=null)n=n.append(o,r);else throw Error("key may not be null if value is not object or array");return n}login(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter loginRequestDto was null or undefined when calling login.");let s=this.defaultHeaders,a=i&&i.httpHeaderAccept;if(a===void 0){let f=["*/*"];a=this.configuration.selectHeaderAccept(f)}a!==void 0&&(s=s.set("Accept",a));let c=i&&i.context;c===void 0&&(c=new z);let u=["application/json"],l=this.configuration.selectHeaderContentType(u);l!==void 0&&(s=s.set("Content-Type",l));let d="json";return a&&(a.startsWith("text")?d="text":this.configuration.isJsonMime(a)?d="json":d="blob"),this.httpClient.request("post",`${this.configuration.basePath}/auth/login`,{context:c,body:n,responseType:d,withCredentials:this.configuration.withCredentials,headers:s,observe:r,reportProgress:o})}register(n,r="body",o=!1,i){if(n==null)throw new Error("Required parameter registerRequestDto was null or undefined when calling register.");let s=this.defaultHeaders,a=i&&i.httpHeaderAccept;if(a===void 0){let f=["*/*"];a=this.configuration.selectHeaderAccept(f)}a!==void 0&&(s=s.set("Accept",a));let c=i&&i.context;c===void 0&&(c=new z);let u=["application/json"],l=this.configuration.selectHeaderContentType(u);l!==void 0&&(s=s.set("Content-Type",l));let d="json";return a&&(a.startsWith("text")?d="text":this.configuration.isJsonMime(a)?d="json":d="blob"),this.httpClient.request("post",`${this.configuration.basePath}/auth/register`,{context:c,body:n,responseType:d,withCredentials:this.configuration.withCredentials,headers:s,observe:r,reportProgress:o})}static{this.\u0275fac=function(r){return new(r||e)(N(zt),N(wr,8),N(le,8))}}static{this.\u0275prov=A({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();export{Je as a,It as b,k as c,nl as d,Pr as e,Fr as f,Ee as g,bt as h,Mt as i,fe as j,dn as k,fl as l,pl as m,Re as n,H as o,Il as p,Pe as q,gn as r,Cl as s,bl as t,Fe as u,bs as v,kr as w,_t as x,Lr as y,Ml as z,jr as A,_s as B,Vr as C,Tl as D,xl as E,Nl as F,Br as G,Sl as H,$r as I,_ as J,fa as K,A as L,qo as M,SE as N,x as O,E as P,N as Q,O as R,Nt as S,Ve as T,AE as U,Jo as V,Ko as W,ei as X,Pa as Y,ge as Z,ka as _,$a as $,OE as aa,RE as ba,PE as ca,FE as da,kE as ea,Cf as fa,hi as ga,mt as ha,gi as ia,it as ja,LE as ka,jE as la,Rf as ma,mi as na,VE as oa,BE as pa,Bn as qa,$E as ra,ae as sa,xo as ta,au as ua,J as va,cr as wa,Oh as xa,jh as ya,Ro as za,Vh as Aa,ur as Ba,Wh as Ca,Yh as Da,rg as Ea,og as Fa,pu as Ga,hu as Ha,mg as Ia,WE as Ja,wg as Ka,GE as La,qE as Ma,Mg as Na,gu as Oa,_g as Pa,YE as Qa,ZE as Ra,QE as Sa,JE as Ta,KE as Ua,XE as Va,lr as Wa,wu as Xa,Si as Ya,eC as Za,Oi as _a,tC as $a,tm as ab,Fi as bb,_C as cb,Eu as db,pr as eb,Bi as fb,im as gb,Su as hb,TC as ib,xC as jb,NC as kb,SC as lb,AC as mb,OC as nb,fm as ob,fr as pb,qC as qb,YC as rb,le as sb,Rm as tb,Om as ub};
