import{a as i}from"./chunk-6FPAJVTP.js";import{a as l,c as f,d as C,f as v}from"./chunk-H277OY4J.js";import{Ia as s,R as r,Ra as u,U as c,qb as h,rb as d,sb as a}from"./chunk-T3NICLM4.js";var k=(()=>{class t{constructor(){this.title="jobup-frontend"}static{this.\u0275fac=function(o){return new(o||t)}}static{this.\u0275cmp=c({type:t,selectors:[["app-root"]],standalone:!0,features:[u],decls:1,vars:0,template:function(o,m){o&1&&s(0,"router-outlet")},dependencies:[f]})}}return t})();var e=(t,n)=>{let p=r(i),o=r(C);return p.hasToken()?!0:(o.navigate(["/sign-in"]),!1)};var g=[{path:"sign-in",loadComponent:()=>import("./chunk-YTREHCWW.js").then(t=>t.SignInComponent)},{path:"sign-up",loadComponent:()=>import("./chunk-YGR7ZHF3.js").then(t=>t.SignUpComponent)},{path:"home",loadComponent:()=>import("./chunk-TCHBVNUB.js").then(t=>t.HomeComponent),canActivate:[e]},{path:"workers",loadComponent:()=>import("./chunk-C5HSG3S5.js").then(t=>t.WorkerListComponent),canActivate:[e]},{path:"workers/create",loadComponent:()=>import("./chunk-QHBFIJAC.js").then(t=>t.CreateWorkerComponent),canActivate:[e]},{path:"workers/:id",loadComponent:()=>import("./chunk-NIFDVZX6.js").then(t=>t.WorkerDetailComponent),canActivate:[e]},{path:"workers/:id/edit",loadComponent:()=>import("./chunk-S2P3OLD2.js").then(t=>t.WorkerEditComponent),canActivate:[e]},{path:"",redirectTo:"/home",pathMatch:"full"},{path:"**",redirectTo:"/sign-in"}];var A={production:!0,apiBaseUrl:"https://jobup-backend.onrender.com/JobUp",keycloakUrl:"https://your-production-keycloak-url/"};var S=(t,n)=>{let o=r(i).getToken();if(t.url.includes("/auth/"))return n(t);if(o){let m=t.clone({headers:t.headers.set("Authorization",`Bearer ${o}`)});return n(m)}return n(t)};var b=new a({basePath:A.apiBaseUrl}),w={providers:[v(g),h(d([S])),{provide:a,useValue:b}]};l(k,w).catch(t=>console.error(t));
